/*
 * @Description:全局变量
 * @Author: 小雨
 * @Date: 2023-03-09 17:06:49
 * @LastEditTime: 2023-03-09 17:07:51
 * @LastEditors: 小雨
 */
import {
	ref
} from "vue";

let baseUrl = '';
let reportUrl = '';
let wsUrl = '';
let DbayLineNumFull = 1750
let CTLineNumFull = 3520
let DbayLineNum = 1500
let CTLineNum = 3000
let address = ref('')
console.log(process.env.NODE_ENV);
if (process.env.NODE_ENV == 'development') {
	// baseUrl = 'http://*************:8085/';
	// reportUrl = 'http://*************:8091/testReport/' //报告
	// wsUrl = 'ws://*************:8085/deviceCctLink' //websocket
	// baseUrl = 'http://*************:8085/';
	// reportUrl = 'http://*************:8091/testReport/' //报告
	
	wsUrl = 'ws://*************:8091/conceptual/deviceCctLink'
	baseUrl = 'http://*************:8091/conceptual/';
	reportUrl = 'https://*************:8091/conceptual/testReport/' //报告
} else {
	wsUrl = 'ws://**************:21112/conceptual/deviceCctLink' //websocket
	baseUrl = 'http://**************:21112/conceptual/';
	reportUrl = 'http://**************:21112/conceptual/report/' //报告
}
const changeAddress = (value) => {
	address.value = value
}

export {
	baseUrl,
	reportUrl,
	wsUrl,
	CTLineNum,
	DbayLineNum,
	CTLineNumFull,
	DbayLineNumFull,
	changeAddress,
	address
};