# WebSocket连接问题分析报告

## 问题描述

在测评工单页面 (`pages/assessment/index.vue`) 中，WebSocket连接出现不断重连的情况，而使用ApiPost等工具连接相同地址可以稳定保持1分钟连接。

## 问题分析

### 1. 日志分析

从提供的日志可以看出：
- WebSocket连接URL: `ws://*************:8091/conceptual/workOrderLink`
- 连接请求发送成功，但立即出现连接错误
- 系统不断进行重连（第2次、第3次、第4次、第5次重连）
- 每次重连都是相同的模式：连接请求成功 → 立即连接错误 → 开始下一次重连

### 2. 代码问题分析

#### 问题1：缺少workOrderId参数
根据需求描述，WebSocket连接需要在header中传递以下参数：
- token ✅ (已实现)
- deviceId ✅ (已实现) 
- **workOrderId** ❌ (缺失)
- sourceType ✅ (已实现)

在 `pages/assessment/index.vue` 的 `initWebSocketConnection()` 方法中：
```javascript
this.workOrderWS.createWebSocket(
    baseWsUrl,
    token,
    this.deviceId,
    '', // 工单ID在具体请求时传入 - 这里传入的是空字符串！
    'PAD'
);
```

#### 问题2：重连逻辑过于激进
在 `utils/workOrderWebSocket.js` 中：
- 最大重连次数为5次
- 重连延迟为 `2000 * this.reconnectAttempts` 毫秒
- 错误和关闭事件都会触发重连，可能导致重复重连

#### 问题3：连接状态检测不准确
在 `pages/assessment/index.vue` 中使用2秒延迟来检测连接状态：
```javascript
setTimeout(() => {
    if (this.workOrderWS?.getConnectionStatus().isConnected) {
        this.wsConnected = true;
        this.wsConnecting = false;
        console.log('WebSocket连接成功');
    } else {
        this.wsConnecting = false;
        console.log('WebSocket连接失败，将使用直接跳转模式');
    }
}, 2000);
```

这种方式不够准确，应该基于实际的连接事件来判断。

## 解决方案

### 1. 修复workOrderId参数传递

需要在初始化WebSocket连接时传递正确的workOrderId，或者在需要时动态更新连接参数。

### 2. 优化重连策略

- 增加重连间隔时间
- 添加指数退避算法
- 避免重复重连
- 添加连接状态检查

### 3. 改进连接状态管理

- 使用事件驱动的连接状态管理
- 添加连接超时处理
- 优化错误处理逻辑

### 4. 添加连接诊断功能

- 添加连接前的网络检查
- 记录详细的连接失败原因
- 提供连接状态的实时反馈

## 建议的修复步骤

1. **立即修复**：在初始化WebSocket时传递正确的workOrderId
2. **优化重连**：调整重连策略，避免过于频繁的重连
3. **改进状态管理**：使用Promise或事件机制来准确跟踪连接状态
4. **添加诊断**：增加更详细的日志和错误处理

## 对比分析

### ApiPost工具 vs 应用内连接

**ApiPost成功的原因：**
- 可能正确传递了所有必需的header参数
- 没有自动重连逻辑干扰
- 连接建立后保持稳定，没有额外的业务逻辑

**应用内连接失败的原因：**
- 缺少workOrderId参数导致服务器拒绝连接
- 重连逻辑过于激进，可能被服务器识别为异常行为
- 连接状态管理不准确，导致误判连接状态

## 具体修复方案

### 方案1：延迟连接策略（推荐）

不在页面初始化时建立WebSocket连接，而是在用户点击具体工单时才建立连接：

1. **移除页面初始化时的WebSocket连接**
2. **在用户点击工单时建立连接并传递workOrderId**
3. **连接成功后再发送OPEN_ORDER_REQUEST消息**

### 方案2：动态更新连接参数

保持当前的连接方式，但在需要时动态更新workOrderId：

1. **初始连接时不传递workOrderId**
2. **在发送消息时动态添加workOrderId到header**
3. **优化重连逻辑**

### 方案3：连接池管理

为每个workOrderId维护独立的WebSocket连接。

## 推荐实施方案1的具体步骤

### 步骤1：修改页面初始化逻辑

在 `pages/assessment/index.vue` 中：
- 移除 `onLoad` 中的 `initWebSocketConnection()` 调用
- 将WebSocket初始化移到 `openOrder` 方法中

### 步骤2：修改工单打开逻辑

在 `openOrder` 方法中：
1. 先建立WebSocket连接（传递正确的workOrderId）
2. 等待连接成功
3. 发送OPEN_ORDER_REQUEST消息

### 步骤3：优化重连策略

在 `utils/workOrderWebSocket.js` 中：
- 增加重连间隔（使用指数退避）
- 添加最大重连时间限制
- 避免重复重连

### 步骤4：改进连接状态管理

使用Promise或事件机制来准确跟踪连接状态，而不是使用setTimeout。

## 下一步行动

1. ✅ 已确认workOrderId的获取方式：从tableData中的item.workOrderId获取
2. 🔄 实施代码修复（方案1）
3. 🔄 测试连接稳定性
4. 🔄 监控连接状态和错误日志

## 风险评估

- **低风险**：方案1改动较小，逻辑清晰
- **中风险**：需要测试各种网络环境下的连接稳定性
- **高风险**：需要确保不影响现有的业务流程
