# 孩子部分布局修正 - 独立白色背景区域

## 问题描述

在之前的实现中，孩子部分的"测验类"和"量表评估类"被放置在一个统一的白色背景容器中。但根据 [Figma 设计稿](https://www.figma.com/design/JqCBGlxqyEKRJgJi7X2Vtp/%E5%BF%83%E7%90%86%E6%B5%8B%E8%AF%84%E7%B3%BB%E7%BB%9F-pad%E7%AB%AF?node-id=222-2706&t=hMEJ9NqVdbO2vH5p-4) 的实际设计，这两个部分应该是独立的白色背景区域。

## Figma 设计分析

从 Figma 设计稿中可以看到两个独立的白色矩形：

- **Rectangle 1182** (测验类区域)
  - 位置：x: 39, y: 292
  - 尺寸：width: 912, height: 750
  - 圆角：16px

- **Rectangle 1183** (量表评估类区域)  
  - 位置：x: 968, y: 292
  - 尺寸：width: 912, height: 750
  - 圆角：16px

- **区域间距**：968 - 39 - 912 = 17px

## 修正方案

### 1. 模板结构调整

**原有结构**：
```vue
<view class="project-container">
  <view class="project-section">
    <view class="section-column left-column">测验类</view>
    <view class="section-column right-column">量表评估类</view>
  </view>
</view>
```

**修正后结构**：
```vue
<view class="child-section-container">
  <view class="section-box left-section">测验类</view>
  <view class="section-box right-section">量表评估类</view>
</view>
```

### 2. 条件渲染优化

- **孩子部分**：使用独立的 `child-section-container`
- **家长和教师部分**：继续使用统一的 `project-container`

```vue
<!-- 孩子部分独立布局 -->
<view class="child-section-container" 
      v-if="currentTabData.length > 0 && currentGroup && currentGroup.respondentGroupType === 'C'">
  <!-- 独立的左右区域 -->
</view>

<!-- 家长和教师部分统一容器 -->
<view class="project-container" 
      v-if="currentTabData.length > 0 && currentGroup && currentGroup.respondentGroupType !== 'C'">
  <!-- 网格布局 -->
</view>
```

### 3. CSS 样式重构

**新增样式**：
```scss
.child-section-container {
  display: flex;
  gap: 17px; /* 精确匹配 Figma 间距 */
  margin: 0 40px;
  margin-top: -2px;
  flex: 1;
  
  .section-box {
    flex: 1;
    background: #FFFFFF;
    border-radius: 0 0 16px 16px;
    padding: 40px;
    overflow-y: auto;
    
    .section-title {
      font-family: 'Alibaba PuHuiTi';
      font-size: 32px;
      font-weight: 400;
      color: #333333;
      margin-bottom: 32px;
      text-align: left;
      padding-bottom: 12px;
      border-bottom: 1px solid #EEEEEE;
    }
    
    .project-cards {
      display: flex;
      flex-direction: column;
      gap: 32px;
    }
  }
}
```

## 实现细节

### 布局特点

1. **独立背景**：每个区域都有自己的白色背景和圆角
2. **精确间距**：17px 的间距完全匹配 Figma 设计
3. **响应式设计**：flex: 1 确保两个区域等宽
4. **一致的内边距**：40px 内边距保持视觉一致性

### 视觉效果

- ✅ 左右两个独立的白色卡片区域
- ✅ 正确的 17px 间距
- ✅ 16px 底部圆角
- ✅ 与选项卡的无缝连接

### 兼容性

- ✅ 保持原有的项目卡片样式
- ✅ 不影响家长和教师部分的布局
- ✅ 响应式设计适配不同屏幕
- ✅ 保持所有交互功能

## 测试要点

1. **布局验证**：确认左右两个区域是独立的白色背景
2. **间距检查**：验证 17px 的区域间距
3. **功能测试**：确保项目点击和导航功能正常
4. **响应式测试**：在不同屏幕尺寸下检查布局
5. **切换测试**：验证孩子/家长/教师部分的切换正常

## 文件变更

### 主要修改
- `pages/home/<USER>

### 新增样式类
- `.child-section-container`: 孩子部分的容器
- `.section-box`: 独立的白色背景区域

### 移除样式类
- `.project-section`: 原有的统一容器样式
- `.section-column`: 原有的列布局样式

## 总结

此次修正确保了孩子部分的布局完全符合 Figma 设计稿的要求，实现了测验类和量表评估类的独立白色背景区域，提升了视觉层次和用户体验。
