# 患者管理页面导航更新说明

## 修改概述

更新了患者管理界面 `pages/record/index.vue` 中"测评"按钮的跳转逻辑，使其跳转到新创建的测评项目页面。

## 具体修改

### 修改文件
- **文件路径**: `pages/record/index.vue`
- **修改位置**: 第240-245行，`goTest` 函数

### 修改内容

#### 修改前
```javascript
const goTest = (outpatientId, patientName, year) => {
    userStore.outpatientId = outpatientId
    userStore.userName = patientName
    userStore.year = year
    navigateTo('/pages/home/<USER>')
}
```

#### 修改后
```javascript
const goTest = (outpatientId, patientName, year) => {
    userStore.outpatientId = outpatientId
    userStore.userName = patientName
    userStore.year = year
    navigateTo('/pages/home/<USER>')
}
```

## 功能说明

### 跳转流程
1. **用户操作**: 在患者管理页面点击某个患者的"测评"按钮
2. **数据存储**: 系统将患者信息存储到 userStore 中：
   - `outpatientId`: 患者ID
   - `userName`: 患者姓名  
   - `year`: 患者年龄（年份）
3. **页面跳转**: 跳转到新的测评项目页面 `/pages/home/<USER>

### 数据传递
通过 `useUserStore` 传递患者信息：
- **outpatientId**: 用于标识患者
- **userName**: 用于显示患者姓名
- **year**: 用于年龄相关的测评判断

## 影响范围

### 直接影响
- 患者管理页面的"测评"按钮现在跳转到新的测评项目选择页面
- 用户可以看到基于接口数据的动态测评项目列表

### 间接影响
- 提升用户体验：动态显示可用的测评项目
- 支持更灵活的测评项目配置
- 为未来的功能扩展提供基础

## 兼容性

### 向前兼容
- 保持了相同的数据传递方式（通过 userStore）
- 新页面能够正确接收和使用患者信息

### 向后兼容
- 如需回退到原有页面，只需将跳转路径改回 `/pages/home/<USER>
- 不影响其他页面的功能

## 测试建议

### 功能测试
1. **基础跳转**: 验证点击"测评"按钮能正确跳转到新页面
2. **数据传递**: 确认患者信息能正确传递到新页面
3. **页面显示**: 验证新页面能正确显示测评项目列表
4. **项目选择**: 测试各种测评项目的选择和跳转

### 边界测试
1. **网络异常**: 测试接口调用失败时的处理
2. **数据异常**: 测试接口返回异常数据时的处理
3. **权限控制**: 验证不同用户权限下的页面表现

## 后续优化建议

1. **加载优化**: 可以考虑在患者管理页面预加载测评项目数据
2. **缓存机制**: 添加测评项目数据的本地缓存
3. **错误处理**: 完善网络错误和数据异常的用户提示
4. **用户体验**: 添加页面切换动画和加载状态提示

## 相关文件

- **患者管理页面**: `pages/record/index.vue`
- **新测评项目页面**: `pages/home/<USER>
- **用户状态管理**: `stores/user.js`
- **页面导航工具**: `common/uniTool.js`
