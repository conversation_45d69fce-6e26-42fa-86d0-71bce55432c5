# 测评项目选择页面跳转逻辑问题分析

## 问题描述

经过分析，发现测评项目选择页面（`new-index.vue`）与工单详情页面（`detail.vue`）在量表跳转逻辑上存在不一致的问题。

## 问题分析

### 1. 当前测评项目选择页面的跳转逻辑

在 `pages/home/<USER>

```javascript
const startProject = (item) => {
    const quizType = item.quizType
    
    switch (quizType) {
        case 'CPT-STD':
            uni.navigateTo({
                url: `/pages/ivacpt/introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}`
            })
            break
        case 'CPT-MINI':
            uni.navigateTo({
                url: `/pages/ivacpt/introduce?type=mini&projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}`
            })
            break
        case 'PPVT':
            uni.navigateTo({
                url: `/pages/introduce/index?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}`
            })
            break
        case 'QUEST':
        default:
            // 量表评估 - 问题在这里！
            uni.navigateTo({
                url: `/pages/scale/introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}`
            })
            break
    }
}
```

### 2. 工单详情页面的正确跳转逻辑

在 `pages/assessment/detail.vue` 的 `startAssessment` 方法中：

```javascript
if (quizType === 'QUEST' || !quizType) {
    // QUEST 类型或未定义时，跳转至 SNAP-IV 量表介绍页
    uni.navigateTo({
        url: `/pages/scale/snap-iv-introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${this.workOrderId}&traineeId=${this.traineeId}`
    })
}
```

## 核心问题

**测评项目选择页面的量表跳转路径错误！**

- ❌ 错误路径：`/pages/scale/introduce`
- ✅ 正确路径：`/pages/scale/snap-iv-introduce`

## 页面结构分析

### 量表相关页面结构

1. **`/pages/scale/introduce`** - 通用量表介绍页面（旧版本）
2. **`/pages/scale/snap-iv-introduce`** - SNAP-IV 量表专用介绍页面（新版本）
3. **`/pages/scale/snap-iv-assessment`** - SNAP-IV 量表测评页面
4. **`/pages/scale/index`** - 旧版量表测评页面

### 正确的跳转链路

对于 QUEST 类型的量表项目：
1. 项目选择页面 → 2. SNAP-IV 介绍页面 → 3. SNAP-IV 测评页面

## 数据支持情况

### 项目数据结构

通过接口 `project/v1.1/qryConceptualProjectList` 返回的数据包含：

```json
{
  "detailProjectInfoList": [
    {
      "projectId": "937595988623888384",
      "projectName": "儿童抑郁障碍目评量表（DSCRS）",
      "quizType": "QUEST",
      "categoryId": "0",
      "iconUrl": "...",
      "simpleDesc": "..."
    }
  ]
}
```

**关键字段**：
- `quizType`: 用于判断项目类型的关键字段
- `projectId`: 项目唯一标识
- `projectName`: 项目名称

**数据支持充分**：现有数据结构完全支持不同量表的区分和跳转。

## 影响范围

### 受影响的功能

1. **量表测评无法正常进行**：用户点击量表项目后，跳转到错误页面
2. **用户体验问题**：不同量表都进入相同界面，无法区分具体量表类型
3. **功能不一致性**：项目选择页面与工单详情页面行为不一致

### 不受影响的功能

1. **IVA-CPT 测评**：CPT-STD 和 CPT-MINI 跳转正确
2. **PPVT 测评**：跳转路径正确
3. **工单详情页面**：跳转逻辑正确

## 解决方案

需要修正测评项目选择页面的跳转逻辑，使其与工单详情页面保持一致。

### 修复要点

1. 将 QUEST 类型的跳转路径从 `/pages/scale/introduce` 改为 `/pages/scale/snap-iv-introduce`
2. 确保参数传递正确（不需要 workOrderId 和 traineeId，因为这是直接测评模式）
3. 保持其他类型项目的跳转逻辑不变

## 结论

**问题确认**：测评项目选择页面确实存在量表跳转路径错误的问题，所有 QUEST 类型的量表都跳转到了错误的页面，导致无法正常进行量表测评。

**数据支持**：现有的项目数据结构完全支持不同量表的区分，`quizType` 字段提供了必要的类型信息。

**修复必要性**：需要立即修复此问题，以确保用户能够正常进行量表测评。