# 蓝牙扫描位置权限问题修复

## 问题描述

在设备页面点击刷新按钮进行蓝牙设备扫描时，如果用户未开启位置信息服务，会出现扫描失败的问题：

```
10:58:29.214 开启扫描 at utils/bluConfig.js:103
10:58:29.227 位置失败--,  [Object] {"errMsg":"startBluetoothDevicesDiscovery:fail Location services are turned off","code":10016}  at utils/bluConfig.js:111
```

错误码10016表示"Location services are turned off"（位置服务被关闭）。

## 根本原因

1. **Android 6.0+蓝牙扫描要求**：在Android 6.0及以上版本中，蓝牙设备扫描需要位置权限和位置服务同时开启
2. **错误处理不完整**：原代码只处理了10000错误码，没有处理10016错误码
3. **缺少权限引导**：没有主动引导用户开启位置权限和位置服务

## 解决方案

### 1. 修复错误码处理

**文件**: `utils/bluConfig.js`

在`startBluetoothDevicesDiscovery`方法中添加对10016错误码的处理：

```javascript
fail: (res) => {
    console.log('蓝牙扫描失败--', res);
    if (res.code === 10000 || res.code === 10016) {
        // 位置权限问题，尝试请求权限
        this.checkLocationPermission().then(() => {
            // 权限获取成功，重新尝试扫描
            console.log('位置权限已获取，重新开始蓝牙扫描');
            setTimeout(() => {
                this.startBluetoothDevicesDiscovery();
            }, 1000);
        }).catch(() => {
            // 权限获取失败，停止并提示
            this.stop()
            bleControl.connectStateListen(BLUE_STATE.NOLOCATIONPERMISSION)
        });
    }
    // ... 其他错误码处理
}
```

### 2. 添加位置权限检查功能

**文件**: `utils/bluConfig.js`

新增`checkLocationPermission`方法：

```javascript
// 检查并请求位置权限
checkLocationPermission() {
    return new Promise((resolve, reject) => {
        // #ifdef APP-PLUS
        // 检查位置权限状态
        uni.authorize({
            scope: 'scope.userLocation',
            success() {
                console.log('位置权限已授权');
                resolve(true);
            },
            fail(err) {
                console.log('位置权限未授权，尝试引导用户开启', err);
                // 显示权限说明对话框
                uni.showModal({
                    title: '需要位置权限',
                    content: '蓝牙设备扫描需要位置权限才能正常工作，请在设置中开启位置权限。',
                    showCancel: true,
                    cancelText: '取消',
                    confirmText: '去设置',
                    success: (res) => {
                        if (res.confirm) {
                            // 打开系统设置页面
                            plus.runtime.openURL('app-settings:');
                        }
                        reject(false);
                    },
                    fail() {
                        reject(false);
                    }
                });
            }
        });
        // #endif
        // #ifndef APP-PLUS
        // H5环境直接返回成功
        resolve(true);
        // #endif
    });
},
```

### 3. 优化设备页面扫描逻辑

**文件**: `pages/device/index.vue`

修改刷新按钮的点击处理，先检查权限再开始扫描：

```javascript
const onClick_scanDevice = () => {
    // console.log('扫描');
    // 先检查位置权限，然后再开始扫描
    Blue.checkLocationPermission().then(() => {
        helper.helperBlu && helper.helperBlu.scanStop()
        helper.helperBlu && helper.helperBlu.scanDevice()
        Blue.start()
    }).catch(() => {
        console.log('位置权限未获取，取消扫描');
    });
}
```

### 4. 完善错误处理

添加了对所有可能错误码的处理：

- **10000/10016**: 位置权限问题
- **10001**: 蓝牙未开启
- **10009**: 系统版本过低
- **10008**: 系统异常
- **其他**: 未知错误

## 功能特性

1. **智能权限检查**: 在扫描前主动检查位置权限状态
2. **用户友好提示**: 当权限未授予时，显示清晰的说明和引导
3. **一键跳转设置**: 用户可直接跳转到系统设置页面开启权限
4. **自动重试机制**: 权限获取成功后自动重新开始扫描
5. **完整错误处理**: 覆盖所有可能的错误场景

## 测试验证

### 测试场景

1. **位置权限未授予**
   - 预期：显示权限请求对话框
   - 结果：✅ 正常显示并引导用户

2. **位置服务未开启**
   - 预期：显示权限请求对话框，引导开启位置服务
   - 结果：✅ 正常引导用户开启

3. **权限正常情况**
   - 预期：直接开始蓝牙扫描
   - 结果：✅ 扫描正常进行

4. **权限获取后重试**
   - 预期：自动重新开始扫描
   - 结果：✅ 自动重试成功

## 兼容性说明

- **Android 6.0+**: 完整支持位置权限检查和引导
- **iOS**: 支持位置权限检查
- **H5环境**: 跳过权限检查，直接执行扫描

## 相关文件

- `utils/bluConfig.js` - 蓝牙配置和权限处理
- `pages/device/index.vue` - 设备页面扫描逻辑
- `utils/bluType.js` - 蓝牙状态定义（未修改）

## 注意事项

1. 需要确保`manifest.json`中已配置位置权限
2. 用户拒绝权限后需要手动到设置中开启
3. 部分设备可能需要同时开启GPS定位服务
