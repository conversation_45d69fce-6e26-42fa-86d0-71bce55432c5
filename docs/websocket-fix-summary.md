# WebSocket连接问题修复总结

## 问题根本原因

通过分析日志和代码，发现WebSocket连接不断重连的根本原因是：

### 1. 缺少关键参数 workOrderId
- **问题**：服务器要求在header中传递workOrderId，但代码中传递的是空字符串
- **影响**：服务器拒绝连接，导致立即断开
- **证据**：日志显示连接请求成功但立即出现连接错误

### 2. 重连策略过于激进
- **问题**：重连间隔太短（2秒、4秒、6秒），可能被服务器识别为异常行为
- **影响**：频繁重连可能导致IP被临时封禁
- **证据**：ApiPost工具可以稳定连接1分钟，说明服务器本身是正常的

### 3. 连接时机不当
- **问题**：在页面初始化时就建立连接，但此时没有具体的workOrderId
- **影响**：无法传递正确的业务参数
- **证据**：代码中workOrderId被硬编码为空字符串

## 修复方案

### 1. 延迟连接策略 ✅
**变更**：将WebSocket连接从页面初始化移到用户点击具体工单时
**好处**：
- 可以传递正确的workOrderId
- 减少不必要的连接
- 提高连接成功率

### 2. 优化重连机制 ✅
**变更**：
- 减少最大重连次数：5次 → 3次
- 使用指数退避算法：3秒、6秒、12秒
- 添加最大延迟限制：30秒

**好处**：
- 避免过于频繁的重连
- 给服务器更多恢复时间
- 减少网络资源消耗

### 3. 改进连接状态管理 ✅
**变更**：
- 使用Promise管理连接状态
- 基于实际连接事件判断状态
- 移除不准确的setTimeout检测

**好处**：
- 更准确的连接状态判断
- 更好的错误处理
- 更清晰的代码逻辑

## 技术实现细节

### 修改的文件
1. `pages/assessment/index.vue` - 主要业务逻辑
2. `utils/workOrderWebSocket.js` - WebSocket工具类
3. `docs/` - 相关文档

### 关键代码变更

#### 1. 新增设备ID初始化方法
```javascript
async initDeviceId() {
    this.userStore = useUserStore();
    this.deviceId = await getOrGenerateDeviceId();
    console.log('设备ID初始化完成:', this.deviceId);
}
```

#### 2. 新增工单专用连接方法
```javascript
async initWebSocketForOrder(workOrderId) {
    return new Promise((resolve, reject) => {
        // 参数验证
        // 创建连接
        // 状态监听
        // Promise处理
    });
}
```

#### 3. 修改工单打开逻辑
```javascript
async goToDetail(item) {
    try {
        await this.initWebSocketForOrder(item.workOrderId);
        this.sendOpenOrderRequest(item.workOrderId);
    } catch (error) {
        this.navigateToDetail(item); // 失败时直接跳转
    }
}
```

#### 4. 优化重连算法
```javascript
reconnect() {
    const delay = Math.min(
        this.baseReconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
        this.maxReconnectDelay
    );
    setTimeout(() => {
        this.createWebSocket(baseUrl, token, deviceId, workOrderId, sourceType);
    }, delay);
}
```

## 预期效果

### 连接成功场景
- ✅ 1-2秒内建立连接
- ✅ 传递正确的workOrderId参数
- ✅ 稳定保持连接状态
- ✅ 正常收发消息

### 连接失败场景
- ✅ 最多重连3次
- ✅ 重连间隔逐渐增加
- ✅ 最终失败时自动跳转
- ✅ 不会无限重连

### 用户体验
- ✅ 清晰的加载状态提示
- ✅ 快速的响应时间
- ✅ 优雅的错误处理
- ✅ 无感知的降级方案

## 风险评估

### 低风险
- 代码逻辑清晰，易于理解和维护
- 保留了原有的降级方案（直接跳转）
- 不影响已完成和已撤销工单的处理

### 中风险
- 需要在不同网络环境下测试
- 需要验证服务器端对新参数的处理

### 缓解措施
- 详细的测试指南
- 完整的回滚方案
- 充分的日志记录

## 后续监控

### 关键指标
1. **连接成功率**：应该显著提高
2. **重连频率**：应该显著降低
3. **用户体验**：加载时间和成功率

### 监控方法
1. 查看控制台日志
2. 统计连接成功/失败次数
3. 收集用户反馈

## 结论

这次修复解决了WebSocket连接的根本问题：
1. **参数完整性**：确保传递所有必需的参数
2. **连接时机**：在合适的时机建立连接
3. **重连策略**：使用更合理的重连机制

预期可以彻底解决不断重连的问题，同时提供更好的用户体验。
