# 测评项目列表动态图标显示功能实现

## 概述

根据接口 `project/v1.1/qryConceptualProjectList` 返回数据中新增的 `iconUrl` 字段，更新了测评项目页面的图标显示方式，从固定的本地图片改为使用服务器返回的动态图标URL。

## 修改内容

### 1. 接口数据结构变化

接口返回的每个项目数据中新增了 `iconUrl` 字段：

```json
{
  "projectId": "937595988623888384",
  "iconUrl": "https://124.222.67.60:9900/resources/quickAieve/png/question.png",
  "projectName": "儿童抑郁障碍目评量表（DSCRS）",
  "quizType": "QUEST",
  "categoryId": "0",
  "simpleDesc": null
}
```

### 2. 页面修改详情

**文件路径**: `pages/home/<USER>

#### 2.1 孩子部分测验类项目图标修改

**修改前**:
```vue
<view class="project-icon test-icon" :class="getProjectIconClass(item.quizType)">
</view>
```

**修改后**:
```vue
<view class="project-icon test-icon" :style="{ backgroundImage: item.iconUrl ? `url(${item.iconUrl})` : '' }">
</view>
```

#### 2.2 孩子部分量表评估类项目图标修改

**修改前**:
```vue
<view class="project-icon quest-icon">
</view>
```

**修改后**:
```vue
<view class="project-icon quest-icon" :style="{ backgroundImage: item.iconUrl ? `url(${item.iconUrl})` : '' }">
</view>
```

#### 2.3 家长和教师部分项目图标修改

**修改前**:
```vue
<view class="project-icon quest-icon">
</view>
```

**修改后**:
```vue
<view class="project-icon quest-icon" :style="{ backgroundImage: item.iconUrl ? `url(${item.iconUrl})` : '' }">
</view>
```

### 3. CSS样式更新

**修改前**:
```scss
.project-icon {
  // ... 其他样式
  &.quest-icon {
    background: #E6F4FF;
    background-image: url('/static/figma-assets/questionnaire-icon.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }
  
  &.test-icon {
    background: #FFFFFF;
    
    &.iva-cpt {
      background-image: url('/static/figma-assets/iva-cpt-icon.png');
      // ... 更多固定图片配置
    }
    // ... 更多类型的固定图片配置
  }
}
```

**修改后**:
```scss
.project-icon {
  // ... 其他样式
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
  
  &.quest-icon {
    background-color: #E6F4FF; /* 使用background-color而不是background */
  }
  
  &.test-icon {
    background-color: #FFFFFF;
  }
}
```

## 技术要点

### 1. 动态背景图片设置
使用Vue的 `:style` 绑定来动态设置背景图片：
```vue
:style="{ backgroundImage: item.iconUrl ? `url(${item.iconUrl})` : '' }"
```

### 2. CSS属性分离
- 将 `background` 属性分离为 `background-color` 和其他背景相关属性
- 统一设置 `background-size: contain`、`background-repeat: no-repeat`、`background-position: center`
- 确保动态设置的 `background-image` 不会被固定样式覆盖

### 3. 兼容性处理
- 当 `iconUrl` 为空时，不设置背景图片
- 保持原有的背景色和边框样式
- 保留图标容器的尺寸和布局

## 影响范围

### 修改的页面
- `pages/home/<USER>

### 影响的功能
- 孩子部分测验类项目图标显示
- 孩子部分量表评估类项目图标显示  
- 家长部分项目图标显示
- 教师部分项目图标显示

## 测试建议

### 1. 图标显示测试
- 验证各类型项目的图标是否正确显示
- 检查图标尺寸和位置是否正常
- 确认图标加载失败时的降级处理

### 2. 兼容性测试
- 测试不同设备上的图标显示效果
- 验证网络较慢时的图标加载体验
- 检查图标URL为空时的显示效果

### 3. 功能测试
- 确认点击项目卡片的跳转功能正常
- 验证标签页切换时图标显示正确
- 测试页面刷新后图标重新加载

## 注意事项

1. **图片加载性能**: 服务器返回的图标URL应该是优化过的图片，建议使用适当的尺寸和格式
2. **网络异常处理**: 当图标URL无法访问时，页面应该有合适的降级显示
3. **缓存策略**: 建议在服务器端为图标资源设置适当的缓存策略，提升加载速度
4. **HTTPS兼容**: 确保图标URL使用HTTPS协议，避免混合内容警告

## 后续优化建议

1. **图标预加载**: 可以考虑在页面加载时预加载图标，提升用户体验
2. **占位图**: 为图标加载失败的情况添加默认占位图
3. **懒加载**: 对于长列表，可以考虑实现图标的懒加载机制
4. **图标缓存**: 在客户端实现图标缓存机制，减少重复请求
