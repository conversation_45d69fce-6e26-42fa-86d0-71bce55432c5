# 工单WebSocket功能实现说明

## 功能概述

本次实现了基于WebSocket的工单设备冲突检测和处理功能，当用户从测评工单页面进入测评详情页面时，会自动进行设备冲突检测，如果发现其他设备正在执行该工单，会弹出相应的确认弹窗。

## 实现的功能

### 1. 工单WebSocket管理类 (`utils/workOrderWebSocket.js`)

基于现有WebSocket实现，创建了专门用于工单链接的WebSocket类：

- **接口路径**: `/workOrderLink`
- **连接参数**: 
  - `token`: 请求token
  - `deviceId`: 设备Id(或者设备ip)
  - `workOrderId`: 工单Id
  - `sourceType`: 参数来源（值为PAD）

- **支持的消息类型**:
  - `OPEN_ORDER`: 打开工单请求
  - `CAN_OPEN_ORDER`: 可以打开工单
  - `NEED_CONFIRM_SWITCH`: 需要确认切换工单
  - `CONFIRM_SWITCH`: 确认切换工单
  - `NEED_CHECK_PASSWORD`: 需要验证密码
  - `VERIFY_PASSWORD`: 验证密码
  - `PASSWORD_ERROR`: 密码验证失败
  - `PASSWORD_RIGHT`: 密码校验成功
  - `CLOSE_ORDER`: 通知其他设备退出
  - `REQUIRE_EXIT`: 要求立刻退出
  - `ACTIVE_EXIT`: 主动退出
  - `ACTIVE_EXIT_SUCCESS`: 已成功退出工单

- **消息格式**:
  所有发送的消息都必须包含以下完整字段：
  ```json
  {
      "type": "消息类型",
      "workOrderId": "工单ID",
      "message": "消息内容（可为空字符串）",
      "passwordLast4": "密码后4位（可为空字符串）"
  }
  ```

### 2. 设备ID获取工具 (`utils/deviceUtils.js`)

实现了跨平台的设备唯一标识获取：

- **H5环境**: 使用浏览器指纹生成设备ID
- **App环境**: 使用系统提供的设备信息生成唯一标识
- **小程序环境**: 基于系统信息组合生成设备ID
- **缓存机制**: 支持设备ID本地缓存，避免重复生成

### 3. 弹窗组件

#### 设备冲突确认弹窗 (`components/DeviceConflictModal.vue`)
- 显示设备冲突提示信息
- 提供"取消"和"确认切换"按钮
- 按照Figma设计稿实现UI样式

#### 密码验证弹窗 (`components/PasswordVerifyModal.vue`)
- 用于输入登录密码后4位
- 支持数字输入验证
- 显示错误提示信息
- 自动聚焦输入框

#### 退出确认弹窗 (`components/ExitConfirmModal.vue`)
- 用户主动退出时的密码验证
- 提供"取消"和"确认退出"按钮
- 支持密码输入和验证

### 4. 测评详情页面集成 (`pages/assessment/detail.vue`)

在测评详情页面集成了完整的WebSocket功能：

- **页面进入时**: 自动建立WebSocket连接并发送打开工单请求
- **设备冲突处理**: 根据服务端响应显示相应弹窗
- **密码验证**: 支持被动退出和主动退出的密码验证
- **页面退出**: 清理WebSocket连接

## 交互流程

### 1. 进入测评详情页面流程

```
用户进入页面
    ↓
获取设备ID和用户token
    ↓
建立WebSocket连接
    ↓
发送OPEN_ORDER
    ↓
服务端响应:
├─ CAN_OPEN_ORDER → 正常进入
├─ NEED_CONFIRM_SWITCH → 显示设备冲突弹窗
    ├─ 用户点击取消 → 返回上一页
    └─ 用户点击确认 → 发送CONFIRM_SWITCH
        ├─ CAN_OPEN_ORDER → 正常进入
        └─ NEED_CHECK_PASSWORD → 显示密码验证弹窗
            ├─ 密码正确 → 发送CLOSE_ORDER → 正常进入
            └─ 密码错误 → 显示错误提示
```

### 2. 主动退出测评流程

```
用户点击退出测评按钮
    ↓
显示退出确认弹窗
    ↓
用户输入密码后4位
    ↓
发送VERIFY_PASSWORD
    ↓
服务端响应:
├─ PASSWORD_RIGHT → 发送ACTIVE_EXIT → 退出成功
└─ PASSWORD_ERROR → 显示错误提示
```

### 3. 被动退出流程

```
服务端发送REQUIRE_EXIT
    ↓
显示强制退出提示
    ↓
用户确认后自动退出
```

## 技术特点

### 1. 响应式设计
- 所有弹窗组件都支持响应式适配
- 在不同屏幕尺寸下保持良好的显示效果

### 2. 错误处理
- 完善的WebSocket连接错误处理
- 支持自动重连机制
- 密码验证错误提示

### 3. 状态管理
- 清晰的WebSocket连接状态管理
- 弹窗显示状态控制
- 加载状态指示

### 4. 内存管理
- 页面卸载时自动清理WebSocket连接
- 避免内存泄漏

## 配置说明

### WebSocket服务器配置

在 `common/global.js` 中配置WebSocket服务器地址：

```javascript
// 开发环境
wsUrl = 'ws://*************:8091/conceptual/deviceCctLink'

// 生产环境  
wsUrl = 'ws://**************:21112/conceptual/deviceCctLink'
```

工单WebSocket会自动将路径替换为 `/workOrderLink`。

### 用户信息获取

通过 `useUserStore()` 获取用户信息，需要确保：
- 用户已登录
- `userInfo.token` 存在
- `userInfo.userId` 存在

## 注意事项

1. **设备ID生成**: 不同平台的设备ID生成策略不同，建议在实际部署时根据具体需求调整
2. **WebSocket连接**: 确保服务端支持 `/workOrderLink` 接口
3. **密码验证**: 密码后4位的验证逻辑需要与服务端保持一致
4. **错误处理**: 建议根据实际业务需求完善错误处理逻辑
5. **Token获取**: 系统会优先从userStore获取token，如果失败会从本地存储获取备用token
6. **重试机制**: WebSocket初始化失败时会自动重试最多2次，避免临时性问题导致功能不可用

## 问题排查

### WebSocket初始化失败

如果遇到"WebSocket初始化失败：缺少必要参数"错误，可以通过以下步骤排查：

1. **检查控制台日志**: 查看详细的参数检查日志和调试信息
2. **验证用户登录状态**: 确保用户已正确登录且token有效
3. **检查工单ID**: 确保从上一页面正确传递了workOrderId参数
4. **验证设备ID**: 确保设备ID生成成功
5. **检查网络连接**: 确保设备网络连接正常

### 常见问题解决方案

1. **Token不存在**:
   - 检查用户是否已登录
   - 验证本地存储中是否有token
   - 重新登录获取新token

2. **设备ID获取失败**:
   - 检查设备权限设置
   - 验证uni-app平台兼容性
   - 使用fallback机制生成临时ID

3. **WebSocket连接失败**:
   - 检查服务端WebSocket服务状态
   - 验证网络连接
   - 检查防火墙设置

## 测试建议

1. **多设备测试**: 使用多个设备同时访问同一工单，验证冲突检测功能
2. **网络异常测试**: 测试网络断开重连的情况
3. **密码验证测试**: 测试正确和错误密码的处理
4. **页面生命周期测试**: 测试页面切换时WebSocket连接的处理

## 后续优化建议

1. **连接状态指示**: 可以添加WebSocket连接状态的可视化指示
2. **离线处理**: 考虑网络离线时的处理策略
3. **消息队列**: 可以考虑添加消息队列机制处理网络不稳定的情况
4. **日志记录**: 添加详细的日志记录便于问题排查
