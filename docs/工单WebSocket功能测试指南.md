# 工单WebSocket功能测试指南

## 测试环境准备

### 1. 服务端配置
确保服务端支持 `/workOrderLink` WebSocket接口，并实现以下消息类型的处理：
- `OPEN_ORDER` - 打开工单请求
- `CONFIRM_SWITCH` - 确认切换工单
- `VERIFY_PASSWORD` - 验证密码
- `ACTIVE_EXIT` - 主动退出

**消息格式要求**：
所有消息都必须包含完整的字段结构：
```json
{
    "type": "消息类型",
    "workOrderId": "工单ID",
    "message": "消息内容（可为空）",
    "passwordLast4": "密码后4位（可为空）"
}
```

### 2. 客户端配置
- 确保用户已登录，token有效
- 确保网络连接正常
- 准备多个测试设备或浏览器标签页

## 测试用例

### 测试用例1：正常进入工单（无冲突）

**测试步骤：**
1. 使用设备A登录系统
2. 进入测评工单列表页面
3. 点击某个工单进入详情页面

**预期结果：**
- WebSocket连接成功
- 发送 `OPEN_ORDER` 消息（格式：`{"type":"OPEN_ORDER","workOrderId":"xxx","message":"","passwordLast4":""}`）
- 收到 `CAN_OPEN_ORDER` 响应
- 正常显示工单详情，无弹窗

**验证点：**
- 控制台显示"WebSocket连接成功，发送打开工单请求"
- 控制台显示"可以打开工单"
- 页面正常显示，无任何弹窗
- 发送的消息格式正确，包含所有必需字段

### 测试用例2：设备冲突检测

**测试步骤：**
1. 使用设备A进入某个工单详情页面
2. 使用设备B进入同一个工单详情页面

**预期结果：**
- 设备B显示设备冲突确认弹窗
- 弹窗内容："发现有另一台设备已在执行该工单，是否确认到当前设备，另一台设备将自动退出。"
- 提供"取消"和"确认切换"按钮

**验证点：**
- 设备B收到 `NEED_CONFIRM_SWITCH` 消息
- 弹窗样式符合Figma设计稿
- 按钮功能正常

### 测试用例3：设备冲突 - 取消操作

**前置条件：** 完成测试用例2

**测试步骤：**
1. 在设备B的冲突确认弹窗中点击"取消"

**预期结果：**
- 弹窗关闭
- 自动返回上一页（工单列表）
- 设备A继续正常使用

**验证点：**
- 设备B成功返回工单列表页面
- 设备A的工单详情页面不受影响

### 测试用例4：设备冲突 - 确认切换（无需密码）

**前置条件：** 完成测试用例2

**测试步骤：**
1. 在设备B的冲突确认弹窗中点击"确认切换"

**预期结果：**
- 发送 `CONFIRM_SWITCH` 消息
- 如果服务端返回 `CAN_OPEN_ORDER`，则正常进入
- 设备A收到退出通知

**验证点：**
- 设备B正常显示工单详情
- 设备A显示强制退出提示

### 测试用例5：设备冲突 - 确认切换（需要密码）

**前置条件：** 完成测试用例2

**测试步骤：**
1. 在设备B的冲突确认弹窗中点击"确认切换"
2. 服务端返回 `NEED_CHECK_PASSWORD`
3. 显示密码验证弹窗

**预期结果：**
- 显示密码输入弹窗
- 弹窗内容："该工单已在另一台设备执行，请输入登录密码后4位，确认当前设备退出该工单"
- 提供密码输入框和"确认退出"按钮

**验证点：**
- 弹窗样式符合Figma设计稿
- 输入框只能输入4位数字
- 输入框自动聚焦

### 测试用例6：密码验证 - 正确密码

**前置条件：** 完成测试用例5

**测试步骤：**
1. 在密码输入框中输入正确的密码后4位
2. 点击"确认退出"

**预期结果：**
- 发送 `VERIFY_PASSWORD` 消息
- 收到 `PASSWORD_RIGHT` 响应
- 发送 `CLOSE_ORDER` 消息
- 设备B正常进入工单详情
- 设备A收到退出通知

**验证点：**
- 密码验证成功
- 设备切换成功
- 原设备正确退出

### 测试用例7：密码验证 - 错误密码

**前置条件：** 完成测试用例5

**测试步骤：**
1. 在密码输入框中输入错误的密码后4位
2. 点击"确认退出"

**预期结果：**
- 发送 `VERIFY_PASSWORD` 消息
- 收到 `PASSWORD_ERROR` 响应
- 显示错误提示："密码验证失败，请重新输入"
- 弹窗保持显示，可重新输入

**验证点：**
- 错误提示正确显示
- 可以重新输入密码
- 3秒后错误提示自动消失

### 测试用例8：主动退出测评

**前置条件：** 设备正常进入工单详情页面

**测试步骤：**
1. 点击左上角"退出测评"按钮
2. 在退出确认弹窗中输入密码后4位
3. 点击"确认退出"

**预期结果：**
- 显示退出确认弹窗
- 弹窗内容："是否确认退出、请输入登录密码后4位，确认退出该工单"
- 密码验证成功后发送 `ACTIVE_EXIT` 消息
- 收到 `ACTIVE_EXIT_SUCCESS` 后退出页面

**验证点：**
- 弹窗样式符合Figma设计稿
- 密码验证流程正确
- 成功退出并返回上一页

### 测试用例9：主动退出 - 取消操作

**前置条件：** 设备正常进入工单详情页面

**测试步骤：**
1. 点击左上角"退出测评"按钮
2. 在退出确认弹窗中点击"取消"

**预期结果：**
- 弹窗关闭
- 继续停留在工单详情页面
- 不发送任何WebSocket消息

**验证点：**
- 弹窗正确关闭
- 页面状态不变

### 测试用例10：网络异常处理

**测试步骤：**
1. 进入工单详情页面
2. 断开网络连接
3. 重新连接网络

**预期结果：**
- 网络断开时显示连接异常提示
- 网络恢复时自动重连（如果在重连次数限制内）
- 重连成功后功能正常

**验证点：**
- 错误提示正确显示
- 重连机制正常工作
- 重连后功能恢复

### 测试用例11：已完成工单的处理

**测试步骤：**
1. 进入状态为"已完成"的工单详情页面
2. 点击"退出测评"按钮

**预期结果：**
- 不建立WebSocket连接
- 直接退出，无需密码验证
- 显示提示："网络连接异常，部分功能可能受限"

**验证点：**
- 无WebSocket连接尝试
- 退出流程简化
- 功能降级提示正确

## 性能测试

### 1. 连接性能
- 测试WebSocket连接建立时间
- 测试消息发送响应时间
- 测试重连机制的性能影响

### 2. 内存泄漏测试
- 多次进入退出工单详情页面
- 检查WebSocket连接是否正确清理
- 监控内存使用情况

### 3. 并发测试
- 多个设备同时访问同一工单
- 测试服务端处理能力
- 验证消息处理的正确性

## 兼容性测试

### 1. 平台兼容性
- H5浏览器环境
- App环境（iOS/Android）
- 不同版本的uni-app

### 2. 设备兼容性
- 不同屏幕尺寸的设备
- 不同分辨率的显示效果
- 响应式布局的适配

## 错误场景测试

### 1. 服务端异常
- 服务端WebSocket服务不可用
- 服务端返回异常消息格式
- 服务端响应超时

### 2. 客户端异常
- 用户token过期
- 设备ID获取失败
- 本地存储异常

## 测试工具推荐

1. **WebSocket测试工具**：可以使用浏览器开发者工具或专门的WebSocket测试工具
2. **网络模拟工具**：用于测试网络异常情况
3. **多设备测试**：使用多个物理设备或浏览器标签页
4. **性能监控工具**：监控内存使用和性能指标

## 测试报告模板

每个测试用例应记录：
- 测试环境信息
- 测试步骤执行情况
- 实际结果与预期结果对比
- 发现的问题和建议
- 测试通过/失败状态
