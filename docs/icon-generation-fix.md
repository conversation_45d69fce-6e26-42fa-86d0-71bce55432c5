# 应用图标配置修复说明

## 问题描述

项目打包时提示以下错误：
```
[HBuilder] 17:47:14.932 Manifest.json文件以下节点配置错误，请检查修复
[HBuilder] 17:47:14.932    app-plus.distribute.icons.android.hdpi	文件不存在
[HBuilder] 17:47:14.932    app-plus.distribute.icons.android.xhdpi	文件不存在
[HBuilder] 17:47:14.932    app-plus.distribute.icons.android.xxhdpi	文件不存在
[HBuilder] 17:47:14.932    app-plus.distribute.icons.android.xxxhdpi	文件不存在
```

## 解决方案

### 1. 源文件
使用项目中的 `static/psychology_app_logo.svg` 作为图标源文件。

### 2. 生成的图标文件
从 SVG 文件生成了以下尺寸的 PNG 图标：

#### Android 图标
- `72x72.png` - hdpi
- `96x96.png` - xhdpi  
- `144x144.png` - xxhdpi
- `192x192.png` - xxxhdpi

#### iOS 图标
- `1024x1024.png` - App Store
- `76x76.png` - iPad app
- `152x152.png` - iPad app@2x
- `20x20.png` - iPad notification
- `40x40.png` - iPad notification@2x / iPad spotlight
- `167x167.png` - iPad proapp@2x
- `29x29.png` - iPad settings
- `58x58.png` - iPad settings@2x
- `80x80.png` - iPad spotlight@2x
- `120x120.png` - iPhone app@2x / iPhone spotlight@3x
- `180x180.png` - iPhone app@3x
- `60x60.png` - iPhone notification@3x
- `87x87.png` - iPhone settings@3x

### 3. 文件位置
所有图标文件已保存到：`unpackage/res/icons/` 目录

### 4. 配置文件
`manifest.json` 中的图标配置路径已经正确指向生成的文件：

```json
"icons": {
    "android": {
        "hdpi": "unpackage/res/icons/72x72.png",
        "xhdpi": "unpackage/res/icons/96x96.png",
        "xxhdpi": "unpackage/res/icons/144x144.png",
        "xxxhdpi": "unpackage/res/icons/192x192.png"
    },
    "ios": {
        // ... iOS 图标配置
    }
}
```

## 修复结果

✅ 创建了 `unpackage/res/icons/` 目录  
✅ 从 SVG 生成了 17 个不同尺寸的 PNG 图标  
✅ 验证了 `manifest.json` 配置正确  
✅ 所有 Android 必需的图标文件现在都存在  

现在项目应该可以正常打包，不会再出现图标文件不存在的错误。

## 技术细节

使用了 `cairosvg` Python 库来将 SVG 文件转换为各种尺寸的 PNG 图标。这确保了：
- 图标质量高，支持矢量缩放
- 所有尺寸的图标保持一致的视觉效果
- 符合 Android 和 iOS 的图标规范