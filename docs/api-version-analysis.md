# 接口版本使用情况分析报告

## 概述
本文档分析了项目中4个接口的旧版本使用情况，这些接口都有对应的新版本（v1.1），需要了解旧版本的使用位置以便进行升级。

## 接口分析详情

### 1. IVA_CPT 测评数据提交接口

#### 1.1 提交std版IVA_CPT测评数据
- **新版本接口**: `/ivacpt/v1.1/subIvacptStdData`
- **旧版本接口**: `/ivacpt/1.0/subIvacptStdData`

**旧版本使用位置:**
- **服务定义**: `service/ivacpt.js:29`
  ```javascript
  export const subIvacptStdData = (data) => {
      return httpRequest.post('ivacpt/1.0/subIvacptStdData', data);
  };
  ```

- **页面调用**:
  1. `pages/ivacpt/practice.vue:324` - 练习页面数据提交
     ```javascript
     subIvacptStdData({
         evaluatId: state.ivaData.evaluatId,
         outpatientId: userStore.outpatientId,
         type: num,
         eventInfos: eventInfos.value
     })
     ```

  2. `pages/ivacpt/standard.vue:376` - 标准版测评页面数据提交
     ```javascript
     subIvacptStdData({
         evaluatId: state.ivaData.evaluatId,
         outpatientId: userStore.outpatientId,
         type: num,
         eventInfos: eventInfos.value
     })
     ```

#### 1.2 提交mini版IVA_CPT测评数据
- **新版本接口**: `/ivacpt/v1.1/subIvacptMiniData`
- **旧版本接口**: `/ivacpt/1.0/subIvacptMiniData`

**旧版本使用位置:**
- **服务定义**: `service/ivacpt.js:39`
  ```javascript
  export const subIvacptMiniData = (data) => {
      return httpRequest.post('ivacpt/1.0/subIvacptMiniData', data);
  };
  ```

- **页面调用**:
  1. `pages/ivacpt/index.vue:351` - Mini版测评页面数据提交
     ```javascript
     subIvacptMiniData({
         evaluatId: state.ivaData.evaluatId,
         outpatientId: userStore.outpatientId,
         type: num,
         eventInfos: eventInfos.value
     })
     ```

### 2. PPTV 免费测评信息接口

#### 2.1 新增pptv免费测评信息
- **新版本接口**: `/ppvtEvaluation/v1.1/addFreeEvaInfo`
- **旧版本接口**: `/ppvtEvaluation/1.0/addFreeEvaInfo`

**旧版本使用位置:**
- **服务定义**: `service/index.js:38`
  ```javascript
  export const addFreeEvaInfo = (data) => {
      return httpRequest.post('ppvtEvaluation/1.0/addFreeEvaInfo', data);
  };
  ```

- **页面调用**:
  1. `pages/question/index.vue:263` - 游戏结束时提交测评信息
     ```javascript
     addFreeEvaInfo({
         evaConsumTime: state.time,
         outpatientId: userStore.outpatientId,
         signalData: state.answer,
         userName: userStore.userName
     })
     ```

  2. `pages/question/index.vue:309` - 问题索引达到121时提交测评信息
     ```javascript
     addFreeEvaInfo({
         evaConsumTime: state.time,
         outpatientId: userStore.outpatientId,
         signalData: state.answer,
         userName: userStore.userName
     })
     ```

  3. `pages/question/test.vue` - 测试页面中也有引用（第74行）

### 3. SNAP 免费测评信息接口

#### 3.1 新增snap免费测评信息
- **新版本接口**: `/SnapEvaluation/v1.1/addSnapEvaInfo`
- **旧版本接口**: `/SnapEvaluation/1.0/addSnapEvaInfo`

**旧版本使用位置:**
- **服务定义**: `service/scale.js:18`
  ```javascript
  export const addSnapEvaInfo = (data) => {
      return httpRequest.post('SnapEvaluation/1.0/addSnapEvaInfo', data);
  };
  ```

- **页面调用**:
  1. `pages/scale/index.vue:149` - 量表测评完成后提交数据
     ```javascript
     addSnapEvaInfo({
         signalData: questionConfig.answers,
         outpatientId: userStore.outpatientId,
         userName: userStore.userName,
     })
     ```

## 总结

### 需要升级的文件列表:
1. **服务层文件**:
   - `service/ivacpt.js` - 更新两个IVA_CPT接口版本
   - `service/index.js` - 更新PPTV接口版本
   - `service/scale.js` - 更新SNAP接口版本

2. **页面文件**:
   - `pages/ivacpt/practice.vue` - 标准版练习页面
   - `pages/ivacpt/standard.vue` - 标准版测评页面
   - `pages/ivacpt/index.vue` - Mini版测评页面
   - `pages/question/index.vue` - PPTV问题页面
   - `pages/question/test.vue` - PPTV测试页面
   - `pages/scale/index.vue` - SNAP量表页面

### 升级建议:
1. 首先更新服务层的接口地址，将版本从 `1.0` 升级到 `v1.1`
2. 测试各个页面功能，确保接口调用正常
3. 注意新版本接口可能有参数或返回值的变化，需要相应调整业务逻辑

### 接口使用频率:
- **IVA_CPT相关接口**: 在3个页面中使用，主要用于注意力测评数据提交
- **PPTV接口**: 在2个页面中使用，主要用于图片词汇测试结果提交
- **SNAP接口**: 在1个页面中使用，主要用于量表测评结果提交

## projectId 和 workorderId 参数获取情况分析

### 📊 参数获取能力总结

经过详细分析，以下是各个使用旧版接口的页面对 `projectId` 和 `workorderId` 参数的获取能力：

| 页面路径 | 接口类型 | projectId | workorderId | 获取方式 | 状态 |
|---------|---------|-----------|-------------|----------|------|
| `pages/ivacpt/practice.vue` | IVA_CPT std版 | ✅ | ✅ | URL参数传递 | **已完成改造** |
| `pages/ivacpt/standard.vue` | IVA_CPT std版 | ✅ | ✅ | URL参数传递 | **已完成改造** |
| `pages/ivacpt/index.vue` | IVA_CPT mini版 | ✅ | ✅ | URL参数传递 | **已完成改造** |
| `pages/question/index.vue` | PPTV免费测评 | ✅ | ✅ | URL参数传递 | **已完成改造** |
| `pages/question/test.vue` | PPTV免费测评 | ✅ | ✅ | URL参数传递 | **已完成改造** |
| `pages/scale/index.vue` | SNAP免费测评 | ✅ | ✅ | URL参数传递 | **已完成改造** |

### 🔍 详细分析结果

#### 1. **IVA_CPT 相关页面**
- **`pages/ivacpt/practice.vue`**: 
  - 只使用 `userStore.outpatientId` 作为用户标识
  - 通过 `props.evaluatId` 获取评估ID
  - **无法获取** `projectId` 和 `workorderId`

- **`pages/ivacpt/standard.vue`**: 
  - 只使用 `userStore.outpatientId` 作为用户标识
  - 通过 `props.evaluatId` 和 `props.type` 获取参数
  - **无法获取** `projectId` 和 `workorderId`

- **`pages/ivacpt/index.vue`**: 
  - 只使用 `userStore.outpatientId` 作为用户标识
  - **无法获取** `projectId` 和 `workorderId`

#### 2. **PPTV 相关页面**
- **`pages/question/index.vue`**: 
  - 只使用 `userStore.outpatientId` 和 `userStore.userName`
  - **无法获取** `projectId` 和 `workorderId`

- **`pages/question/test.vue`**: 
  - 从代码结构看，只引用了 `addFreeEvaInfo` 函数
  - **无法获取** `projectId` 和 `workorderId`

#### 3. **SNAP 相关页面**
- **`pages/scale/index.vue`**: 
  - 只使用 `userStore.outpatientId` 和 `userStore.userName`
  - **无法获取** `projectId` 和 `workorderId`

### ✅ **传参改造完成情况**

**改造已完成！所有使用旧版接口的页面现在都可以获取到 `projectId` 和 `workorderId` 参数**

### 🚨 关键发现

1. **发现参数传递链路**：
   - `pages/assessment/detail.vue` → 介绍页面 → 测评页面
   - `pages/home/<USER>
   - 参数通过 URL 传递：`?projectId=xxx&workOrderId=xxx&traineeId=xxx&projectName=xxx`

2. **项目中确实存在这两个参数的使用**，主要在以下场景：
   - 工单管理相关页面 (`pages/assessment/detail.vue`, `pages/assessment/index.vue`)
   - 新版量表页面 (`pages/scale/snap-iv-assessment.vue`) 
   - WebSocket 连接管理
   - 工单记录查询

3. **参数传递模式**：
   - 新版页面通过 URL 参数传递：`?projectId=xxx&workOrderId=xxx`
   - 例如：`/pages/scale/snap-iv-introduce?projectId=${item.projectId}&workOrderId=${this.workOrderId}`

### 🔧 **改造内容总结**

#### 1. **介绍页面改造**
- ✅ `pages/ivacpt/introduce.vue` - 添加参数接收和传递
- ✅ `pages/introduce/index.vue` - 添加参数接收和传递  
- ✅ `pages/scale/introduce.vue` - 添加参数接收和传递

#### 2. **测评页面改造**
- ✅ `pages/ivacpt/practice.vue` - 添加参数接收，修改接口调用
- ✅ `pages/ivacpt/standard.vue` - 添加参数接收，修改接口调用
- ✅ `pages/ivacpt/index.vue` - 添加参数接收，修改接口调用
- ✅ `pages/question/index.vue` - 添加参数接收，修改接口调用
- ✅ `pages/question/test.vue` - 添加参数接收
- ✅ `pages/scale/index.vue` - 添加参数接收，修改接口调用

#### 3. **服务层接口升级**
- ✅ `service/ivacpt.js` - 升级到 v1.1 版本
- ✅ `service/index.js` - 升级到 v1.1 版本
- ✅ `service/scale.js` - 升级到 v1.1 版本

#### 4. **接口调用修改**
所有接口调用都已添加 `projectId` 和 `workorderId` 参数：
```javascript
// 示例：IVA_CPT接口调用
subIvacptStdData({
  evaluatId: state.ivaData.evaluatId,
  outpatientId: userStore.outpatientId,
  type: num,
  eventInfos: eventInfos.value,
  projectId: props.projectId,        // 新增
  workorderId: props.workOrderId     // 新增
})
```

### 💡 升级建议

#### 方案一：修改页面路由传参
1. **修改所有跳转到这些页面的路由**，添加 `projectId` 和 `workorderId` 参数
2. **修改页面接收参数**，在 `onLoad` 或 `setup` 中获取这两个参数
3. **修改接口调用**，将参数传递给新版接口

#### 方案二：保持兼容性
1. **在接口调用时判断参数是否存在**
2. **如果参数不存在，使用默认值或从其他地方获取**
3. **逐步迁移页面，添加参数支持**

#### 方案三：分阶段升级
1. **第一阶段**：保持旧版接口调用不变
2. **第二阶段**：修改页面支持参数传递
3. **第三阶段**：升级到新版接口

### 📋 具体修改清单

如果选择方案一，需要修改以下内容：

1. **路由跳转修改**：
   - 所有跳转到上述6个页面的地方都需要添加参数
   - 搜索项目中的 `navigateTo`、`redirectTo` 等跳转方法

2. **页面参数接收**：
   ```javascript
   // 在每个页面添加参数接收
   onLoad((options) => {
     if (options.projectId) {
       projectId.value = options.projectId;
     }
     if (options.workorderId) {
       workorderId.value = options.workorderId;
     }
   });
   ```

3. **接口调用修改**：
   ```javascript
   // 在接口调用时添加新参数
   subIvacptStdData({
     evaluatId: state.ivaData.evaluatId,
     outpatientId: userStore.outpatientId,
     type: num,
     eventInfos: eventInfos.value,
     projectId: projectId.value,    // 新增
     workorderId: workorderId.value // 新增
   })
   ```

---
*生成时间: 2024年*
*分析范围: 整个项目代码库*
