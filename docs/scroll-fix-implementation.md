# 测评项目页面标签内容区域滚动修复

## 问题描述
当前测评项目页面（new-index.vue）的各个标签页（家长、孩子、教师）下面的内容区域，在内容较多时无法独立滚动查看超过显示区域的内容。

## 问题原因
1. **内容区域滚动缺失**：各标签页的内容容器没有正确设置独立滚动
2. **孩子部分布局问题**：左右两个区域（测验类、量表评估类）无法独立滚动
3. **Flex布局高度限制**：内容区域使用 `flex: 1` 但没有设置 `overflow-y: auto`

## 修复方案

### 1. 页面主容器保持固定布局
```scss
.assessment-project-page {
	width: 100vw;
	height: 100vh; // 保持固定高度，不滚动整个页面
	background: #F6F6F6;
	display: flex;
	flex-direction: column;
	overflow: hidden; // 页面主容器不滚动
	
	position: relative;
}
```

### 2. 家长和教师部分内容区域滚动
```scss
.project-container {
	flex: 1;
	margin: 0 40px;
	margin-top: 16px;
	background: #FFFFFF;
	border-radius: 0 0 12px 12px;
	padding: 40px;
	overflow-y: auto; // 启用垂直滚动
	overflow-x: hidden; // 隐藏水平滚动条
	
	scroll-behavior: smooth;
	-webkit-overflow-scrolling: touch;
	padding-bottom: 20px;
}
```

### 3. 孩子部分左右区域独立滚动
```scss
.child-section-container {
	display: flex;
	gap: 17px;
	margin: 0 40px;
	margin-top: 16px;
	flex: 1;
	
	.section-box {
		flex: 1;
		background: #FFFFFF;
		border-radius: 0 0 12px 12px;
		padding: 40px;
		overflow-y: auto; // 启用垂直滚动，左右区域独立滚动
		overflow-x: hidden; // 隐藏水平滚动条
		
		scroll-behavior: smooth;
		-webkit-overflow-scrolling: touch;
		padding-bottom: 20px;
	}
}
```

## 修复要点

1. **页面布局固定**：
   - 主容器保持固定高度 `height: 100vh` 和 `overflow: hidden`
   - 页面整体不滚动，保持固定布局结构

2. **标签内容区域独立滚动**：
   - 家长和教师部分：`.project-container` 设置 `overflow-y: auto`
   - 孩子部分左右区域：`.section-box` 各自设置 `overflow-y: auto`
   - 每个内容区域可以独立滚动，互不影响

3. **滚动区域明确**：
   - 只有内容区域可以滚动，顶部标题栏和标签页固定
   - 孩子部分的测验类和量表评估类可以分别独立滚动
   - 避免整个页面滚动造成的布局混乱

4. **兼容性保持**：
   - 保留原有的滚动优化属性：`scroll-behavior: smooth`
   - 保留移动端滚动优化：`-webkit-overflow-scrolling: touch`
   - 维持响应式设计的完整性

## 测试验证

修复后应验证以下场景：
1. ✅ 家长标签页：内容超出显示区域时可以独立滚动
2. ✅ 教师标签页：内容超出显示区域时可以独立滚动
3. ✅ 孩子标签页：测验类区域可以独立滚动
4. ✅ 孩子标签页：量表评估类区域可以独立滚动
5. ✅ 标签页切换时，各区域的滚动位置保持独立
6. ✅ 顶部标题栏和标签页保持固定不滚动
7. ✅ 响应式布局在不同屏幕尺寸下滚动正常
8. ✅ 移动端触摸滚动体验良好

## 影响范围

- **修改文件**：`pages/home/<USER>
- **影响功能**：各标签页内容区域的独立滚动体验
- **兼容性**：保持所有现有功能和样式不变
- **性能影响**：无负面影响，提供更好的用户体验

## 注意事项

1. 页面整体布局保持固定，只有内容区域可以滚动
2. 滚动条只在内容超出显示区域时显示
3. 孩子部分的左右两个区域可以独立滚动
4. 保持了原有的视觉设计和交互体验
5. 响应式设计的所有断点都已相应调整
6. 标签页切换时，各区域的滚动位置互不影响
