# 测评工单页面WebSocket连接流程说明

## 流程概述

根据需求，测评工单页面的WebSocket连接流程已经修改为：
1. **页面初始化**：不建立WebSocket连接，只初始化必要的参数
2. **点击工单详情**：建立WebSocket连接并传入对应的workOrderId
3. **连接成功**：发送工单打开请求，等待服务器响应
4. **连接失败**：直接跳转到详情页面（降级方案）

## 详细流程图

```
用户进入页面
    ↓
初始化设备ID (不建立WebSocket连接)
    ↓
显示工单列表
    ↓
用户点击工单详情
    ↓
判断工单状态
    ↓
已完成/已撤销? ──→ 是 ──→ 直接跳转详情页面
    ↓ 否
显示"正在连接..."
    ↓
建立WebSocket连接(传入workOrderId)
    ↓
连接成功? ──→ 否 ──→ 重连(最多3次) ──→ 失败 ──→ 直接跳转详情页面
    ↓ 是
显示"正在加载..."
    ↓
发送OPEN_ORDER_REQUEST消息
    ↓
等待服务器响应
    ↓
收到CAN_OPEN_ORDER ──→ 跳转详情页面
    ↓
收到NEED_CONFIRM_SWITCH ──→ 显示密码确认弹窗
    ↓
收到REQUIRE_EXIT ──→ 显示设备冲突提示
```

## 关键代码实现

### 1. 页面初始化 (onLoad)
```javascript
async onLoad() {
    // 只初始化设备ID，不建立WebSocket连接
    await this.initDeviceId();
    this.getList();
}
```

### 2. 设备ID初始化
```javascript
async initDeviceId() {
    try {
        this.userStore = useUserStore();
        this.deviceId = await getOrGenerateDeviceId();
        console.log('设备ID初始化完成:', this.deviceId);
    } catch (error) {
        console.error('设备ID初始化失败:', error);
    }
}
```

### 3. 点击工单详情
```javascript
async goToDetail(item) {
    console.log('跳转到详情页面:', item);
    
    // 如果工单已完成或已撤销，直接跳转
    if (item.state === 'DONE' || item.state === 'CANCELED') {
        this.navigateToDetail(item);
        return;
    }
    
    // 显示连接状态
    uni.showLoading({ title: '正在连接...' });
    
    // 保存当前工单信息
    this.currentWorkOrderId = item.workOrderId;
    this.currentWorkOrderItem = item;
    this.isOpeningOrder = true;
    
    try {
        // 为当前工单建立WebSocket连接
        await this.initWebSocketForOrder(item.workOrderId);
        
        // 连接成功后发送打开工单请求
        uni.showLoading({ title: '正在加载...' });
        this.sendOpenOrderRequest(item.workOrderId);
        this.setOpenOrderTimeout();
        
    } catch (error) {
        console.error('WebSocket连接失败，使用直接跳转模式:', error);
        uni.hideLoading();
        this.isOpeningOrder = false;
        this.navigateToDetail(item);
    }
}
```

### 4. WebSocket连接建立
```javascript
async initWebSocketForOrder(workOrderId) {
    return new Promise((resolve, reject) => {
        try {
            // 参数验证
            if (!workOrderId) {
                reject(new Error('workOrderId不能为空'));
                return;
            }
            
            if (!this.deviceId) {
                reject(new Error('设备ID未初始化'));
                return;
            }
            
            // 获取token
            let token = this.userStore?.userInfo?.token;
            if (!token) {
                token = uni.getStorageSync('token');
            }
            
            if (!token) {
                reject(new Error('未找到token'));
                return;
            }
            
            // 清理之前的连接
            if (this.workOrderWS) {
                this.workOrderWS.closeSocket();
            }
            
            // 创建新的WebSocket连接
            this.workOrderWS = new WorkOrderWebSocketClass();
            this.workOrderWS.setMessageCallback(this.handleWebSocketMessage);
            
            console.log(`准备为工单 ${workOrderId} 建立WebSocket连接`);
            
            this.wsConnecting = true;
            
            // 建立连接，传入正确的workOrderId
            this.workOrderWS.createWebSocket(
                wsUrl,
                token,
                this.deviceId,
                workOrderId, // 关键：传入正确的工单ID
                'PAD'
            );
            
            // 监听连接状态
            const checkConnection = () => {
                const status = this.workOrderWS?.getConnectionStatus();
                if (status?.isConnected) {
                    this.wsConnected = true;
                    this.wsConnecting = false;
                    console.log(`工单 ${workOrderId} 的WebSocket连接成功`);
                    resolve(true);
                } else if (status?.reconnectAttempts >= 3) {
                    this.wsConnecting = false;
                    console.log(`工单 ${workOrderId} 的WebSocket连接失败，重连次数超限`);
                    reject(new Error('WebSocket连接失败'));
                } else {
                    setTimeout(checkConnection, 500);
                }
            };
            
            setTimeout(checkConnection, 1000);
            
        } catch (error) {
            console.error(`工单 ${workOrderId} 的WebSocket初始化失败:`, error);
            this.wsConnecting = false;
            reject(error);
        }
    });
}
```

## 连接参数说明

### WebSocket连接URL
```
ws://*************:8091/conceptual/workOrderLink
```

### Header参数
```javascript
{
    "token": "JWT认证token",
    "deviceId": "32位设备唯一标识",
    "workOrderId": "点击的工单ID", // 关键参数
    "sourceType": "PAD"
}
```

## 重连策略

### 重连配置
- **最大重连次数**: 3次
- **重连延迟**: 指数退避算法
  - 第1次: 3秒
  - 第2次: 6秒  
  - 第3次: 12秒
- **最大延迟**: 30秒

### 重连触发条件
- WebSocket连接错误
- WebSocket连接关闭（非用户主动关闭）

### 重连停止条件
- 达到最大重连次数
- 用户主动关闭连接
- 连接成功

## 降级方案

当WebSocket连接失败时，系统会自动降级到直接跳转模式：
1. 隐藏加载提示
2. 重置连接状态
3. 直接跳转到工单详情页面
4. 用户可以正常查看和操作工单

## 用户体验

### 正常流程
1. 点击工单 → "正在连接..." → "正在加载..." → 跳转详情页面

### 异常流程
1. 点击工单 → "正在连接..." → 连接失败 → 直接跳转详情页面

### 状态提示
- **正在连接...**: WebSocket连接建立中
- **正在加载...**: 连接成功，等待服务器响应
- **请求超时**: 5秒内未收到服务器响应

## 注意事项

1. **每次点击都会建立新连接**：确保传递正确的workOrderId
2. **自动清理旧连接**：避免连接泄漏
3. **优雅降级**：连接失败不影响基本功能
4. **状态管理**：准确跟踪连接和业务状态
5. **错误处理**：完善的异常处理机制
