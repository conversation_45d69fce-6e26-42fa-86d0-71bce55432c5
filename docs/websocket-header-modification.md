# WebSocket连接参数传递方式修改说明

## 修改概述

将WebSocket连接时的参数传递方式从URL查询参数改为通过header传递。

## 修改的文件

### 1. `/utils/workOrderWebSocket.js`

#### 主要修改：

1. **移除URL查询参数构建**：
   - 删除了`buildQueryString`函数
   - 不再将参数拼接到URL中

2. **添加header参数支持**：
   ```javascript
   // 构建header参数
   const headers = {
       'token': token || '',
       'deviceId': deviceId || '',
       'sourceType': sourceType || 'PAD'
   };
   
   // 只有当workOrderId不为空时才添加到header中
   if (workOrderId && workOrderId.trim() !== '') {
       headers['workOrderId'] = workOrderId;
   }
   ```

3. **区分H5和APP-PLUS环境**：
   - **APP-PLUS环境**：直接通过`uni.connectSocket`的`header`参数传递
   - **H5环境**：连接建立后发送认证消息（因为WebSocket构造函数不支持header）

4. **H5环境认证消息**：
   ```javascript
   // H5环境下需要手动发送连接参数作为认证消息
   if (this.connectionParams && this.connectionParams.headers) {
       const authMessage = {
           type: 'AUTH',
           ...this.connectionParams.headers
       };
       this.ws.send(JSON.stringify(authMessage));
   }
   ```

5. **更新测试连接方法**：
   - `testSimpleConnection`方法现在支持传递header参数
   - 在H5环境下会发送认证消息进行测试

### 2. `/pages/assessment/index.vue`

#### 主要修改：

1. **更新测试连接调用**：
   ```javascript
   // 构建测试用的header参数
   const testHeaders = {
       'token': token,
       'deviceId': this.deviceId,
       'sourceType': 'PAD'
   };
   
   // 先测试简单连接（带header参数）
   this.workOrderWS.testSimpleConnection(baseWsUrl, testHeaders);
   ```

2. **更新日志信息**：
   - 日志中现在显示"通过header传递参数"
   - 包含header参数信息

## 参数传递对比

### 修改前（URL查询参数）：
```
ws://*************:8091/conceptual/workOrderLink?token=xxx&deviceId=xxx&workOrderId=xxx&sourceType=PAD
```

### 修改后（Header传递）：

**APP-PLUS环境**：
```javascript
uni.connectSocket({
    url: 'ws://*************:8091/conceptual/workOrderLink',
    header: {
        'token': 'xxx',
        'deviceId': 'xxx', 
        'workOrderId': 'xxx',
        'sourceType': 'PAD'
    }
});
```

**H5环境**：
```javascript
// 1. 连接纯净URL
const ws = new WebSocket('ws://*************:8091/conceptual/workOrderLink');

// 2. 连接建立后发送认证消息
ws.send(JSON.stringify({
    type: 'AUTH',
    token: 'xxx',
    deviceId: 'xxx',
    workOrderId: 'xxx', 
    sourceType: 'PAD'
}));
```

## 优势

1. **URL更简洁**：不再包含敏感信息如token
2. **安全性提升**：token等敏感信息不会出现在URL中
3. **符合标准**：认证信息通过header传递是更标准的做法
4. **兼容性好**：同时支持APP-PLUS和H5环境

## 注意事项

1. **服务器端需要相应修改**：服务器需要支持从header或认证消息中读取参数
2. **H5环境特殊处理**：由于WebSocket构造函数限制，H5环境使用认证消息方式
3. **重连机制保持不变**：重连时会使用相同的header参数
4. **向后兼容**：如果服务器还未更新，可能需要临时保持URL参数方式

## 测试建议

1. 在APP-PLUS环境下测试header传递是否正常
2. 在H5环境下测试认证消息是否被服务器正确处理
3. 测试重连机制是否正常工作
4. 验证所有参数（token、deviceId、workOrderId、sourceType）都能正确传递
