# 返回按钮实现说明

## 概述

在新的测评项目页面 `pages/home/<USER>

## 实现内容

### 1. HTML 结构
在顶部功能区添加了返回按钮：
```html
<!-- 返回按钮 -->
<view class="back-btn" @click="goBack">
    <view class="back-icon"></view>
    <text class="back-text">返回</text>
</view>
```

### 2. JavaScript 方法
添加了返回方法：
```javascript
const goBack = () => {
    uni.navigateBack({
        delta: 1
    })
}
```

### 3. CSS 样式
简洁的返回按钮样式，无背景设计：
```scss
.back-btn {
    position: absolute;
    left: 22px;
    top: 24px;
    width: 154px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 11px;
    cursor: pointer;
    z-index: 10;
    
    &:hover {
        opacity: 0.8;
    }
    
    .back-icon {
        width: 22px;
        height: 22px;
        background: url('/static/nav-back-icon.png') no-repeat center;
        background-size: contain;
        filter: brightness(0) invert(1);  // 将图标转为白色
    }
    
    .back-text {
        font-family: 'Alibaba PuHuiTi';
        font-size: 22px;
        color: #FFFFFF;
        font-weight: 400;
    }
}
```

## 设计特点

### 1. 位置布局
- **位置**: 左上角，与其他页面保持一致
- **坐标**: left: 22px, top: 24px
- **层级**: z-index: 10，确保在其他元素之上

### 2. 视觉设计
- **背景**: 透明（无背景）
- **悬停效果**: 透明度变为 0.8
- **尺寸**: 154px × 44px
- **简洁设计**: 融入顶部蓝色背景

### 3. 图标和文字
- **图标**: 使用 `/static/nav-back-icon.png`，通过 CSS filter 转为白色
- **图标颜色**: 白色（使用 `filter: brightness(0) invert(1)`）
- **文字**: "返回"，白色字体
- **字体**: Alibaba PuHuiTi，22px
- **间距**: 图标和文字间距 11px

### 4. 交互效果
- **点击事件**: 调用 `uni.navigateBack()` 返回上一页
- **悬停效果**: 整体透明度变为 0.8
- **光标样式**: pointer

## 功能说明

### 1. 返回逻辑
```javascript
uni.navigateBack({
    delta: 1  // 返回上一个页面
})
```

### 2. 使用场景
- 用户从患者管理页面跳转到测评项目页面
- 点击返回按钮可以回到患者管理页面
- 保持导航的连贯性和用户体验

### 3. 兼容性
- 支持所有 uni-app 平台
- 与现有页面设计风格一致
- 响应式设计，适配不同屏幕尺寸

## 相关资源

### 1. 图标文件
- **路径**: `/static/nav-back-icon.png`
- **用途**: 返回按钮图标
- **尺寸**: 22px × 22px

### 2. 参考页面
- **设计参考**: `pages/assessment/detail.vue` 的退出按钮
- **样式一致性**: 保持与其他页面相同的设计语言

## 测试建议

### 1. 功能测试
1. 从患者管理页面进入测评项目页面
2. 点击左上角返回按钮
3. 确认能正确返回到患者管理页面

### 2. 视觉测试
1. 检查按钮位置是否正确
2. 验证悬停效果是否正常
3. 确认图标和文字显示正常

### 3. 兼容性测试
1. 在不同设备上测试按钮显示
2. 验证横屏模式下的显示效果
3. 测试不同浏览器的兼容性

## 后续优化建议

1. **动画效果**: 可以添加按钮点击动画
2. **状态反馈**: 添加点击后的视觉反馈
3. **键盘支持**: 支持 ESC 键返回
4. **面包屑导航**: 考虑添加面包屑导航显示当前位置

## 相关文件

- **页面文件**: `pages/home/<USER>
- **图标资源**: `/static/nav-back-icon.png`
- **参考页面**: `pages/assessment/detail.vue`
