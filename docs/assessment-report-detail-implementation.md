# 测评详情界面报告功能实现说明

## 功能概述

在测评详情界面 `pages/assessment/detail.vue` 中，为已完成的测评项目的"详情"按钮添加了获取报告详情的功能。用户点击"详情"按钮后，系统会调用接口获取报告文档，并根据返回的文档类型进行相应处理。

## 实现功能

### 1. API接口集成

在 `service/workorder.js` 中添加了新的API接口：

```javascript
/**
 * @description: 导出项目评估PDF报告
 * @author: 许江涛
 * @param {{ projectId: string, evaluatId: string, traineeId: string }} data
 * @return {Promise<{code: string, msg: string|null, data: {doc?: string, pdf?: string}, map: {}}>}
 */
export const exportProjectEvaluationPdf = (data = {}) => {
  return httpRequest.post('project/v1.1/exportProjectEvaluationPdf', data);
};
```

### 2. 报告文档选择弹窗组件

创建了 `components/ReportDocumentModal.vue` 组件，用于在有多种文档格式时显示选择界面：

**功能特性：**
- 支持PDF和Word文档格式的显示
- 美观的图标和样式设计
- 点击文档项可直接打开对应格式的报告
- 支持取消操作

### 3. 详情按钮功能增强

在 `pages/assessment/detail.vue` 中修改了 `viewDetail` 方法：

**处理逻辑：**
1. **状态检查**：只有已完成（state === '1'）的测评才能获取报告
2. **接口调用**：调用 `exportProjectEvaluationPdf` 接口获取报告文档
3. **文档处理**：
   - 如果只有单一格式（PDF或DOC），直接调用外部程序打开
   - 如果同时有PDF和DOC格式，显示选择弹窗让用户选择
   - 如果没有任何文档，显示相应提示

### 4. 外部程序打开文档

实现了 `openDocumentWithExternalApp` 方法，支持不同平台：

**平台适配：**
- **APP环境**：使用 `plus.runtime.openURL` 调用系统默认程序打开
- **H5环境**：使用 `window.open` 在新窗口打开
- **小程序环境**：复制链接到剪贴板供用户手动访问

## 接口参数说明

### 请求参数
```javascript
{
    "projectId": "937595817865383936",     // 项目ID
    "evaluatId": "QUTE942993140615352320", // 评估ID
    "traineeId": "6687964dd9330ea428d92714" // 患者ID
}
```

### 返回数据
```javascript
{
    "code": "0000",
    "msg": null,
    "data": {
        "doc": "http://*************:9900/resources/quickAieve/doc/王瑾诩_20250817204725.docx",
        "pdf": "http://*************:9900/resources/quickAieve/pdf/王瑾诩_20250817204725.pdf"
    },
    "map": {}
}
```

## 用户体验

### 操作流程
1. 用户在测评详情界面看到已完成的测评项目
2. 点击"详情"按钮
3. 系统显示"获取报告中..."加载提示
4. 根据返回的文档类型：
   - **单一文档**：直接调用外部程序打开
   - **多个文档**：显示选择弹窗，用户选择后打开对应格式

### 错误处理
- 测评未完成时提示"测评未完成，无法获取报告"
- 接口调用失败时提示"获取报告失败，请重试"
- 无报告文档时提示"暂无报告文档"
- 文档打开失败时提示"打开文档失败"

## 技术要点

### 组件通信
- 使用 `props` 传递弹窗显示状态和报告数据
- 使用 `$emit` 发送关闭弹窗和打开文档事件

### 样式设计
- 采用统一的设计语言，与现有界面风格保持一致
- 使用 Alibaba PuHuiTi 字体
- 支持hover效果和过渡动画

### 代码质量
- 添加了完整的JSDoc注释
- 实现了错误处理和用户提示
- 支持多平台兼容性

## 文件变更清单

1. **service/workorder.js** - 添加新的API接口
2. **components/ReportDocumentModal.vue** - 新建报告文档选择弹窗组件
3. **pages/assessment/detail.vue** - 增强详情按钮功能
4. **docs/assessment-report-detail-implementation.md** - 功能实现说明文档

## 测试建议

1. 测试已完成测评的详情按钮功能
2. 测试未完成测评的错误提示
3. 测试单一文档格式的直接打开
4. 测试多文档格式的选择弹窗
5. 测试不同平台的文档打开功能
6. 测试网络错误和接口异常情况

## 注意事项

1. `evaluatId` 参数目前使用 `projectId` 作为值，可能需要根据实际业务逻辑调整
2. 外部程序打开功能依赖于系统环境，需要确保目标设备支持相应的文档格式
3. 小程序环境下只能复制链接，用户需要手动在浏览器中访问
