# 测评工单详情页面WebSocket连接优化说明

## 修改概述

根据用户需求，对测评工单页面 `pages/assessment/index.vue` 的WebSocket连接和消息处理逻辑进行了优化，确保用户体验更加流畅和可靠。

## 主要修改内容

### 1. 统一Toast提示信息

**修改前：**
- 点击详情时先显示"正在连接..."
- WebSocket连接成功后再显示"正在加载..."

**修改后：**
- 统一显示"加载中..."，避免用户看到多次不同的提示信息

### 2. 完善WebSocket连接流程

**优化的连接流程：**
1. 用户点击列表详情
2. 显示"加载中..."提示
3. 建立WebSocket连接
4. 发送 `OPEN_ORDER_REQUEST` 消息
5. 等待服务器响应：
   - 收到 `CAN_OPEN_ORDER` → 直接进入详情页面
   - 收到 `NEED_CONFIRM_SWITCH` → 显示验证码输入弹窗
   - 连接失败或超时 → 显示错误信息

### 3. 增强错误处理机制

**新增错误处理：**
- WebSocket连接失败时显示明确的错误信息
- 消息发送失败时的错误提示
- 请求超时处理（从5秒延长到10秒）
- 连接状态检查优化（最多检查20次，总共10秒）

### 4. 优化验证码验证流程

**验证码验证优化：**
- 密码验证成功后发送 `CLOSE_ORDER` 消息
- 验证发送是否成功，失败时给出明确提示
- 验证成功后更新loading提示为"加载中..."
- 等待 `CAN_OPEN_ORDER` 响应后才进入详情页面

### 5. 改进消息处理逻辑

**消息处理优化：**
- 只在正在打开工单状态下处理WebSocket消息
- 对未知消息类型进行错误处理
- 增加详细的日志记录便于调试

## 核心代码修改

### goToDetail 方法优化

```javascript
async goToDetail(item) {
    // 如果工单已完成或已撤销，直接跳转
    if (item.state === 'DONE' || item.state === 'CANCELED') {
        this.navigateToDetail(item);
        return;
    }

    // 统一显示加载提示
    uni.showLoading({
        title: '加载中...'
    });

    // 保存当前工单信息
    this.currentWorkOrderId = item.workOrderId;
    this.currentWorkOrderItem = item;
    this.isOpeningOrder = true;

    try {
        // 为当前工单建立WebSocket连接
        await this.initWebSocketForOrder(item.workOrderId);

        // 连接成功后发送打开工单请求
        this.sendOpenOrderRequest(item.workOrderId);
        this.setOpenOrderTimeout();

    } catch (error) {
        console.error('WebSocket连接失败:', error);
        uni.hideLoading();
        this.isOpeningOrder = false;
        
        // 显示错误信息
        uni.showToast({
            title: '连接失败，请重试',
            icon: 'none'
        });
    }
}
```

### 新增错误处理方法

```javascript
// 处理WebSocket错误
handleWebSocketError(errorMessage) {
    console.error('WebSocket操作错误:', errorMessage);
    this.clearOpenOrderTimeout();
    uni.hideLoading();
    this.isOpeningOrder = false;
    
    uni.showToast({
        title: errorMessage || '操作失败，请重试',
        icon: 'none'
    });
    
    // 清理当前工单状态
    this.currentWorkOrderId = '';
    this.currentWorkOrderItem = null;
}
```

## 用户体验改进

1. **统一的加载提示**：用户只会看到"加载中..."，避免了提示信息的频繁变化
2. **明确的错误反馈**：连接失败、发送失败等情况都有明确的错误提示
3. **更长的超时时间**：给WebSocket连接更多时间，减少因网络延迟导致的失败
4. **完整的验证流程**：只有在正确的WebSocket响应后才会进入详情页面

## 测试建议

1. **正常流程测试**：
   - 点击待执行工单的详情按钮
   - 验证是否显示"加载中..."
   - 验证是否正确进入详情页面

2. **设备冲突测试**：
   - 在另一设备打开同一工单
   - 点击详情按钮
   - 验证是否显示验证码输入弹窗
   - 输入正确密码后验证是否正确进入详情页面

3. **错误情况测试**：
   - 断网状态下点击详情
   - 验证是否显示连接失败提示
   - WebSocket服务不可用时的错误处理

4. **已完成工单测试**：
   - 点击已完成或已撤销工单的详情
   - 验证是否直接跳转（不走WebSocket流程）

## 注意事项

- 修改后的代码保持了向后兼容性
- 所有原有功能都得到保留
- 增加了更多的日志记录，便于问题排查
- 错误处理更加完善，提升了系统的健壮性
