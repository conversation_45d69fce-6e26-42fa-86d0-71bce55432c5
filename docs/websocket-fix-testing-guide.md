# 测评工单页面WebSocket连接流程修改指南

## 修改内容总结

### 1. 连接流程优化
- ✅ **延迟连接策略**：进入页面时不建立WebSocket连接
- ✅ **按需连接**：只在点击工单详情时才建立连接
- ✅ **正确传参**：连接时传入对应的workOrderId
- ✅ **优化重连**：使用指数退避算法，最多重连3次

### 2. 具体变更

#### 页面初始化逻辑 (`pages/assessment/index.vue`)
**修改前：**
```javascript
// 在onLoad中初始化WebSocket连接
await this.initWebSocketConnection();
```

**修改后：**
```javascript
// 只初始化设备ID，WebSocket连接延迟到需要时建立
await this.initDeviceId();
```

#### 工单打开逻辑
**修改前：**
```javascript
// 检查WebSocket是否已连接，未连接则直接跳转
if (!this.wsConnected) {
    this.navigateToDetail(item);
    return;
}
// 发送打开工单请求
this.sendOpenOrderRequest(item.workOrderId);
```

**修改后：**
```javascript
try {
    // 为当前工单建立WebSocket连接
    await this.initWebSocketForOrder(item.workOrderId);
    // 连接成功后发送打开工单请求
    this.sendOpenOrderRequest(item.workOrderId);
} catch (error) {
    // 连接失败则直接跳转
    this.navigateToDetail(item);
}
```

#### WebSocket连接参数
**修改前：**
```javascript
this.workOrderWS.createWebSocket(
    baseWsUrl,
    token,
    this.deviceId,
    '', // 工单ID为空
    'PAD'
);
```

**修改后：**
```javascript
this.workOrderWS.createWebSocket(
    baseWsUrl,
    token,
    this.deviceId,
    workOrderId, // 传递正确的工单ID
    'PAD'
);
```

#### 重连策略优化 (`utils/workOrderWebSocket.js`)
**修改前：**
- 最大重连次数：5次
- 重连延迟：2000 * 重连次数（线性增长）

**修改后：**
- 最大重连次数：3次
- 重连延迟：指数退避算法（3秒、6秒、12秒，最大30秒）

## 测试步骤

### 1. 页面初始化测试
1. **进入测评工单页面**
2. **观察控制台日志**，应该看到：
   ```
   设备ID初始化完成: [32位设备ID]
   ```
3. **确认没有WebSocket连接相关的日志**（说明页面初始化时没有建立连接）

### 2. 点击工单详情测试
1. **点击任意未完成的工单**
2. **观察控制台日志**，应该看到：
   ```
   跳转到详情页面: [工单对象]
   准备为工单 [workOrderId] 建立WebSocket连接
   WorkOrder WebSocket连接URL: ws://*************:8091/conceptual/workOrderLink
   工单 [workOrderId] 的WebSocket连接成功
   ```
3. **确认workOrderId不是空字符串**

### 3. 已完成工单测试
1. **点击状态为'DONE'或'CANCELED'的工单**
2. **观察是否直接跳转到详情页面**（不应该建立WebSocket连接）
3. **确认控制台没有WebSocket连接日志**

### 4. 连接失败测试
1. **断开网络连接**
2. **点击未完成的工单**
3. **观察重连日志**，应该看到：
   ```
   WorkOrder WebSocket开始第1次重连，延迟3000ms
   WorkOrder WebSocket开始第2次重连，延迟6000ms
   WorkOrder WebSocket开始第3次重连，延迟12000ms
   WorkOrder WebSocket重连次数超限，停止重连
   ```
4. **最终应该直接跳转到详情页面**

### 5. 多次点击测试
1. **快速点击不同的工单**
2. **观察是否正确清理之前的连接**
3. **确认每次都传递正确的workOrderId**

### 6. 参数传递测试
1. **点击工单后查看连接日志中的Header参数**
2. **确认包含以下字段**：
   - token: 有效的JWT token
   - deviceId: 32位设备ID
   - workOrderId: 实际的工单ID（与点击的工单ID一致）
   - sourceType: "PAD"
3. **验证workOrderId与点击的工单ID完全匹配**

## 预期效果

### 1. 连接成功场景
- WebSocket连接应该在1-2秒内建立成功
- 不应该出现立即断开和重连的情况
- 连接成功后可以正常发送和接收消息

### 2. 连接失败场景
- 最多重连3次后停止
- 重连间隔逐渐增加（3秒、6秒、12秒）
- 最终失败后直接跳转到详情页面

### 3. 用户体验
- 点击工单后显示"正在连接..."
- 连接成功后显示"正在加载..."
- 连接失败时自动跳转，无需用户干预

## 故障排查

### 如果仍然出现重连问题
1. **检查服务器端是否正确处理workOrderId参数**
2. **确认token是否有效**
3. **检查网络环境是否稳定**

### 如果连接建立但立即断开
1. **检查服务器端的认证逻辑**
2. **确认所有必需的header参数都已正确传递**
3. **查看服务器端日志了解断开原因**

## 回滚方案

如果修复后出现问题，可以通过以下步骤回滚：
1. 恢复页面初始化时的WebSocket连接
2. 移除workOrderId参数传递
3. 恢复原有的重连策略

## 后续优化建议

1. **添加连接状态指示器**：让用户了解当前连接状态
2. **实现连接池管理**：为不同工单维护独立连接
3. **添加网络状态检测**：在网络恢复时自动重连
4. **优化错误提示**：提供更友好的错误信息
