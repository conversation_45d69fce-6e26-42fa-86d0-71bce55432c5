# 测评记录页面查看报告功能实现

## 概述
在测评记录页面实现了与测评详情页面相同的查看报告功能，用户点击"查看报告"按钮后可以获取并打开PDF或Word格式的测评报告。

## 实现内容

### 1. API导入
- 导入了 `exportProjectEvaluationPdf` API 用于获取测评报告

### 2. 数据属性添加
添加了报告选项浮窗相关的数据属性：
```javascript
// 报告选项浮窗相关
showReportModal: false,
reportOptions: [], // 报告选项列表
reportModalPosition: { top: 0, left: 0 }, // 浮窗位置
currentReportItem: null // 当前点击的项目
```

### 3. 核心方法实现

#### viewReport(item, event)
- 调用 `exportProjectEvaluationPdf` API 获取报告
- 支持PDF和Word两种格式的报告
- 如果只有一种格式，直接打开
- 如果有多种格式，显示选择浮窗

#### openDocument(url)
- 支持HTTP到HTTPS的自动转换
- 优先使用外部浏览器打开
- 失败时降级为下载后打开

#### selectReportOption(option)
- 处理用户选择报告格式

#### closeReportModal()
- 关闭报告选项浮窗

### 4. 模板更新
- 修改"查看报告"按钮，添加 `$event` 参数用于定位浮窗位置
- 添加报告选项浮窗HTML结构

### 5. 样式添加
- 添加了报告选项浮窗的CSS样式
- 支持悬停效果和边框分隔

## API调用参数
```javascript
{
    projectId: item.projectId,    // 项目ID
    evaluatId: item.id,          // 测评记录ID
    traineeId: item.traineeId    // 患者ID
}
```

## 功能特性
1. **智能文档处理**：自动检测并支持PDF和Word格式
2. **多格式选择**：当有多种格式时显示选择浮窗
3. **位置智能定位**：浮窗位置基于点击按钮位置计算
4. **安全URL处理**：自动将HTTP转换为HTTPS
5. **降级处理**：外部浏览器打开失败时自动降级为下载打开
6. **用户友好**：提供加载状态和错误提示

## 使用方法
用户在测评记录页面点击任意记录的"查看报告"按钮，系统将：
1. 显示加载状态
2. 调用API获取报告
3. 根据返回的报告格式数量决定直接打开或显示选择浮窗
4. 使用外部浏览器或下载方式打开文档

## 注意事项
- 确保设备支持外部浏览器调用
- 网络环境需要能够访问报告文档URL
- 支持的文档格式：PDF、Word
