# WebSocket消息格式示例

## 消息格式规范

所有WebSocket消息都必须遵循以下JSON格式：

```json
{
    "type": "消息类型",
    "workOrderId": "工单ID",
    "message": "消息内容",
    "passwordLast4": "密码后4位"
}
```

**字段说明：**
- `type`: 必填，消息类型字符串
- `workOrderId`: 必填，工单ID字符串
- `message`: 必填，消息内容字符串（可为空字符串）
- `passwordLast4`: 必填，密码后4位字符串（可为空字符串）

## 客户端发送消息示例

### 1. 打开工单请求

```json
{
    "type": "OPEN_ORDER",
    "workOrderId": "7979797977",
    "message": "",
    "passwordLast4": ""
}
```

### 2. 确认切换工单

```json
{
    "type": "CONFIRM_SWITCH",
    "workOrderId": "7979797977",
    "message": "",
    "passwordLast4": ""
}
```

### 3. 验证密码

```json
{
    "type": "VERIFY_PASSWORD",
    "workOrderId": "7979797977",
    "message": "",
    "passwordLast4": "1234"
}
```

### 4. 关闭工单

```json
{
    "type": "CLOSE_ORDER",
    "workOrderId": "7979797977",
    "message": "",
    "passwordLast4": ""
}
```

### 5. 主动退出

```json
{
    "type": "ACTIVE_EXIT",
    "workOrderId": "7979797977",
    "message": "",
    "passwordLast4": ""
}
```

## 服务端响应消息示例

### 1. 可以打开工单

```json
{
    "type": "CAN_OPEN_ORDER",
    "workOrderId": "7979797977",
    "message": "工单可以正常打开",
    "passwordLast4": ""
}
```

### 2. 需要确认切换

```json
{
    "type": "NEED_CONFIRM_SWITCH",
    "workOrderId": "7979797977",
    "message": "发现有另一台设备已在执行该工单，是否确认切换？",
    "passwordLast4": ""
}
```

### 3. 需要验证密码

```json
{
    "type": "NEED_CHECK_PASSWORD",
    "workOrderId": "7979797977",
    "message": "该工单已在另一台设备执行，请输入登录密码后4位",
    "passwordLast4": ""
}
```

### 4. 密码验证成功

```json
{
    "type": "PASSWORD_RIGHT",
    "workOrderId": "7979797977",
    "message": "密码验证成功",
    "passwordLast4": ""
}
```

### 5. 密码验证失败

```json
{
    "type": "PASSWORD_ERROR",
    "workOrderId": "7979797977",
    "message": "密码验证失败，请重新输入",
    "passwordLast4": ""
}
```

### 6. 要求退出

```json
{
    "type": "REQUIRE_EXIT",
    "workOrderId": "7979797977",
    "message": "发现有其他设备已在执行该工单，请立刻退出",
    "passwordLast4": ""
}
```

### 7. 退出成功

```json
{
    "type": "ACTIVE_EXIT_SUCCESS",
    "workOrderId": "7979797977",
    "message": "已成功退出工单",
    "passwordLast4": ""
}
```

## 完整交互流程示例

### 场景1：正常打开工单

**客户端发送：**
```json
{
    "type": "OPEN_ORDER",
    "workOrderId": "7979797977",
    "message": "",
    "passwordLast4": ""
}
```

**服务端响应：**
```json
{
    "type": "CAN_OPEN_ORDER",
    "workOrderId": "7979797977",
    "message": "工单可以正常打开",
    "passwordLast4": ""
}
```

### 场景2：设备冲突需要切换

**客户端发送：**
```json
{
    "type": "OPEN_ORDER",
    "workOrderId": "7979797977",
    "message": "",
    "passwordLast4": ""
}
```

**服务端响应：**
```json
{
    "type": "NEED_CONFIRM_SWITCH",
    "workOrderId": "7979797977",
    "message": "发现有另一台设备已在执行该工单，是否确认切换？",
    "passwordLast4": ""
}
```

**客户端确认切换：**
```json
{
    "type": "CONFIRM_SWITCH",
    "workOrderId": "7979797977",
    "message": "",
    "passwordLast4": ""
}
```

**服务端要求验证密码：**
```json
{
    "type": "NEED_CHECK_PASSWORD",
    "workOrderId": "7979797977",
    "message": "请输入登录密码后4位",
    "passwordLast4": ""
}
```

**客户端验证密码：**
```json
{
    "type": "VERIFY_PASSWORD",
    "workOrderId": "7979797977",
    "message": "",
    "passwordLast4": "1234"
}
```

**服务端确认密码正确：**
```json
{
    "type": "PASSWORD_RIGHT",
    "workOrderId": "7979797977",
    "message": "密码验证成功",
    "passwordLast4": ""
}
```

### 场景3：主动退出工单

**客户端验证密码：**
```json
{
    "type": "VERIFY_PASSWORD",
    "workOrderId": "7979797977",
    "message": "",
    "passwordLast4": "1234"
}
```

**服务端确认密码：**
```json
{
    "type": "PASSWORD_RIGHT",
    "workOrderId": "7979797977",
    "message": "密码验证成功",
    "passwordLast4": ""
}
```

**客户端主动退出：**
```json
{
    "type": "ACTIVE_EXIT",
    "workOrderId": "7979797977",
    "message": "",
    "passwordLast4": ""
}
```

**服务端确认退出：**
```json
{
    "type": "ACTIVE_EXIT_SUCCESS",
    "workOrderId": "7979797977",
    "message": "已成功退出工单",
    "passwordLast4": ""
}
```

## 注意事项

1. **字段完整性**：所有消息都必须包含4个字段，即使某些字段为空字符串
2. **数据类型**：所有字段值都必须是字符串类型
3. **工单ID一致性**：同一会话中的所有消息的workOrderId必须保持一致
4. **密码格式**：passwordLast4字段只在密码验证时填入4位数字字符串
5. **消息顺序**：客户端应按照业务逻辑顺序发送消息，等待服务端响应后再发送下一条消息

## 错误处理

如果消息格式不正确，服务端可能返回错误消息或关闭连接。客户端应该：

1. 验证消息格式的完整性
2. 确保所有字段都存在且为字符串类型
3. 处理网络异常和连接断开的情况
4. 实现重连和重试机制
