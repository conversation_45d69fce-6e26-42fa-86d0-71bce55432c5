# 测评工单页面WebSocket连接流程修改验证清单

## ✅ 修改完成项目

### 1. 页面初始化逻辑
- ✅ **移除页面初始化时的WebSocket连接**
  - 原代码：`await this.initWebSocketConnection();`
  - 修改后：`await this.initDeviceId();`
- ✅ **只初始化设备ID和用户信息**
- ✅ **不建立任何WebSocket连接**

### 2. 点击工单详情逻辑
- ✅ **在goToDetail方法中建立WebSocket连接**
- ✅ **传入正确的workOrderId参数**
- ✅ **区分已完成和未完成工单的处理**
- ✅ **添加连接状态提示**

### 3. WebSocket连接方法
- ✅ **创建initWebSocketForOrder方法**
- ✅ **使用Promise管理连接状态**
- ✅ **传递完整的header参数**
  - token: JWT认证令牌
  - deviceId: 设备唯一标识
  - workOrderId: 工单ID（关键参数）
  - sourceType: "PAD"
- ✅ **实现连接状态监听**
- ✅ **添加参数验证**

### 4. 重连策略优化
- ✅ **减少最大重连次数：5次 → 3次**
- ✅ **使用指数退避算法**
- ✅ **增加基础延迟：2秒 → 3秒**
- ✅ **添加最大延迟限制：30秒**

### 5. 错误处理和降级
- ✅ **连接失败时自动降级到直接跳转**
- ✅ **清理连接状态**
- ✅ **隐藏加载提示**
- ✅ **重置业务状态**

### 6. 连接管理
- ✅ **自动清理旧连接**
- ✅ **页面卸载时清理连接**
- ✅ **避免连接泄漏**

## 🔍 关键验证点

### 1. 页面初始化验证
```javascript
// 应该只看到这些日志，没有WebSocket连接日志
console.log('设备ID初始化完成:', this.deviceId);
```

### 2. 点击工单验证
```javascript
// 应该看到这些日志
console.log('跳转到详情页面:', item);
console.log(`准备为工单 ${workOrderId} 建立WebSocket连接`);
console.log('WorkOrder WebSocket连接URL:', this.wsUrl);
console.log(`工单 ${workOrderId} 的WebSocket连接成功`);
```

### 3. 参数传递验证
```javascript
// Header参数应该包含正确的workOrderId
{
    "token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "deviceId": "4267A0446FBC49771C776B7B1394E0A7",
    "workOrderId": "实际的工单ID", // 不再是空字符串
    "sourceType": "PAD"
}
```

### 4. 重连机制验证
```javascript
// 重连日志应该显示递增的延迟
console.log('WorkOrder WebSocket开始第1次重连，延迟3000ms');
console.log('WorkOrder WebSocket开始第2次重连，延迟6000ms');
console.log('WorkOrder WebSocket开始第3次重连，延迟12000ms');
console.log('WorkOrder WebSocket重连次数超限，停止重连');
```

## 📋 测试场景

### 场景1：正常连接流程
1. 进入页面 → 只初始化设备ID
2. 点击未完成工单 → 建立WebSocket连接
3. 连接成功 → 发送工单请求
4. 收到响应 → 跳转详情页面

### 场景2：已完成工单
1. 点击已完成/已撤销工单 → 直接跳转详情页面
2. 不建立WebSocket连接

### 场景3：连接失败
1. 点击工单 → 尝试建立连接
2. 连接失败 → 重连3次
3. 重连失败 → 直接跳转详情页面

### 场景4：多次点击
1. 快速点击不同工单 → 自动清理旧连接
2. 为新工单建立连接 → 传递正确的workOrderId

## 🚨 注意事项

### 1. 确保workOrderId正确传递
- 检查item.workOrderId是否存在
- 确认传递给WebSocket的workOrderId与点击的工单ID一致
- 验证服务器端收到的workOrderId参数

### 2. 连接状态管理
- 确保wsConnected和wsConnecting状态正确更新
- 避免状态不一致导致的问题
- 正确处理连接超时

### 3. 内存管理
- 确保旧连接被正确关闭
- 避免WebSocket连接泄漏
- 页面卸载时清理所有资源

### 4. 用户体验
- 提供清晰的加载状态提示
- 连接失败时不影响基本功能
- 避免长时间等待

## 🔧 故障排查

### 如果仍然出现连接问题
1. **检查workOrderId是否正确传递**
2. **确认token是否有效**
3. **验证设备ID是否正确生成**
4. **检查网络连接状态**
5. **查看服务器端日志**

### 如果连接建立但立即断开
1. **检查服务器端认证逻辑**
2. **确认所有header参数格式正确**
3. **验证workOrderId在服务器端是否存在**
4. **检查服务器端的业务逻辑**

## ✅ 最终确认

- [ ] 页面初始化时不建立WebSocket连接
- [ ] 点击工单时才建立连接
- [ ] 正确传递workOrderId参数
- [ ] 重连策略工作正常
- [ ] 连接失败时能正常降级
- [ ] 用户体验良好
- [ ] 没有内存泄漏
- [ ] 日志信息完整清晰

## 📝 部署建议

1. **先在测试环境验证**
2. **监控连接成功率**
3. **收集用户反馈**
4. **准备回滚方案**
5. **逐步推广到生产环境**
