# WebSocket Header参数完整内容打印说明

## 概述

为了便于调试和验证WebSocket连接时header参数的传递情况，在关键位置添加了详细的header参数打印功能。

## 添加的日志打印位置

### 1. WebSocket连接创建时 (`utils/workOrderWebSocket.js`)

**位置**：`createWebSocket`方法中
**打印内容**：
```
=== WebSocket Header参数完整内容 ===
Header对象: {完整JSON格式}
Header[token]: {类型、长度、值（脱敏）、是否为空}
Header[deviceId]: {类型、长度、值、是否为空}
Header[sourceType]: {类型、长度、值、是否为空}
Header[workOrderId]: {类型、长度、值、是否为空} (仅当不为空时)
=== Header参数完整内容结束 ===
```

### 2. H5环境认证消息发送时

**位置**：H5环境的`onopen`事件处理中
**打印内容**：
```
=== H5认证消息完整内容 ===
认证消息对象: {完整JSON格式，包含type: 'AUTH'}
AuthMessage[type]: {类型、长度、值、是否为空}
AuthMessage[token]: {类型、长度、值（脱敏）、是否为空}
AuthMessage[deviceId]: {类型、长度、值、是否为空}
AuthMessage[sourceType]: {类型、长度、值、是否为空}
AuthMessage[workOrderId]: {类型、长度、值、是否为空} (仅当不为空时)
=== H5认证消息完整内容结束 ===
```

### 3. APP-PLUS环境连接成功时

**位置**：APP-PLUS环境的`onOpen`事件处理中
**打印内容**：
```
=== APP-PLUS环境Header参数确认 ===
连接成功时的Header参数: {完整JSON格式}
APP-Header[token]: {类型、长度、值（脱敏）、是否为空}
APP-Header[deviceId]: {类型、长度、值、是否为空}
APP-Header[sourceType]: {类型、长度、值、是否为空}
APP-Header[workOrderId]: {类型、长度、值、是否为空} (仅当不为空时)
=== APP-PLUS环境Header参数确认结束 ===
```

### 4. 测试连接时

**位置**：`testSimpleConnection`方法中
**打印内容**：
```
=== 测试连接Header参数完整内容 ===
测试Header对象: {完整JSON格式}
TestHeader[token]: {类型、长度、值（脱敏）、是否为空}
TestHeader[deviceId]: {类型、长度、值、是否为空}
TestHeader[sourceType]: {类型、长度、值、是否为空}
=== 测试连接Header参数完整内容结束 ===
```

### 5. 页面初始化时 (`pages/assessment/index.vue`)

**位置**：`initWebSocketConnection`方法中
**打印内容**：
```
=== 页面初始化Header参数完整内容 ===
页面Header对象: {完整JSON格式}
PageHeader[token]: {类型、长度、值（脱敏）、是否为空、来源}
PageHeader[deviceId]: {类型、长度、值、是否为空、来源}
PageHeader[sourceType]: {类型、长度、值、是否为空、来源}
=== 页面初始化Header参数完整内容结束 ===
```

### 6. 发送工单请求时

**位置**：`sendOpenOrderRequest`方法中
**打印内容**：
```
=== 发送工单请求时Header参数状态 ===
当前连接Header参数: {完整JSON格式}
请求的工单ID: {具体工单ID}
=== 发送工单请求时Header参数状态结束 ===
```

## 日志信息详解

### 每个参数的详细信息包括：

1. **type**: 参数的JavaScript类型
2. **length**: 参数值的长度（字符串长度）
3. **value**: 参数的值
   - token: 显示前20个字符 + "..."（脱敏处理）
   - 其他参数: 显示完整值
4. **isEmpty**: 是否为空值或空字符串
5. **source**: 参数来源（仅在页面初始化时显示）
   - token: 'userStore/storage'
   - deviceId: 'deviceUtils'  
   - sourceType: 'hardcoded'

### 脱敏处理

为了安全考虑，token参数在日志中会进行脱敏处理：
- 完整JSON对象中token会正常显示
- 单独分析时只显示前20个字符，后面用"..."代替

## 使用方法

1. **开发环境调试**：打开浏览器开发者工具的Console面板
2. **APP调试**：使用uni-app的调试工具或真机调试
3. **查看日志**：按照上述格式查找对应的日志输出

## 调试建议

1. **检查参数完整性**：确认所有必要参数都存在且不为空
2. **验证参数格式**：检查参数类型和长度是否符合预期
3. **对比环境差异**：比较H5和APP-PLUS环境下的参数传递情况
4. **追踪参数来源**：通过source字段了解参数的获取来源
5. **监控连接状态**：结合连接成功/失败日志分析问题

## 注意事项

1. **生产环境**：建议在生产环境中移除或减少这些详细日志
2. **安全性**：虽然token进行了脱敏，但仍需注意日志的安全性
3. **性能影响**：大量日志输出可能对性能有轻微影响
4. **日志过滤**：可以通过搜索"=== "来快速定位这些专门的调试日志
