# 测评项目页面 UI 更新 - 基于 Figma 设计

## 概述

根据 Figma 设计稿 [心理测评系统-pad端](https://www.figma.com/design/JqCBGlxqyEKRJgJi7X2Vtp/%E5%BF%83%E7%90%86%E6%B5%8B%E8%AF%84%E7%B3%BB%E7%BB%9F-pad%E7%AB%AF?node-id=222-2706&t=hMEJ9NqVdbO2vH5p-4) 对 `pages/home/<USER>

## 主要修改内容

### 1. 状态栏更新
- **高度调整**: 从 33px 调整为 48px，匹配 Figma 设计
- **保持白色背景**: 与设计稿一致

### 2. 顶部功能区更新
- **高度调整**: 从 92px 调整为 88px
- **背景色变更**: 从蓝色 (#287FFF) 改为白色 (#FFFFFF)
- **返回按钮优化**:
  - 位置调整: left: 40px, top: 28px
  - 尺寸调整: 32px × 32px
  - 移除白色滤镜，适配白色背景
  - 隐藏"返回"文字，只显示图标
- **标题样式**:
  - 字体大小: 从 28px 调整为 32px
  - 颜色: 从白色改为深色 (#333333)

### 3. 选项卡组合更新
- **布局调整**: 外边距从 27px 28px 调整为 40px
- **背景透明**: 移除白色背景
- **选项卡样式**:
  - 内边距: 从 14px 17px 调整为 20px 24px
  - 圆角: 从 11px 调整为 16px
  - 字体大小: 从 19px 调整为 28px
  - 字体粗细: 从 400 调整为 500
  - 间距: 从 11px 调整为 16px
  - 添加底部边框分隔线

### 4. 项目容器更新
- **外边距**: 从 28px 调整为 40px
- **内边距**: 从 28px 调整为 40px
- **圆角**: 从 11px 调整为 16px

### 5. 分类显示区域更新
- **标题样式**:
  - 字体大小: 从 24px 调整为 32px
  - 字体粗细: 从 600 调整为 400
  - 对齐方式: 从居中改为左对齐
  - 分隔线: 从粗线改为细线 (#EEEEEE)
- **卡片间距**: 从 16px/20px 调整为 32px
- **列间距**: 从 28px 调整为 40px

### 6. 项目卡片重设计
- **尺寸调整**:
  - 图标区域: 从 60×60px 调整为 140×140px
  - 圆角: 从 12px 调整为 16px
  - 内边距: 32px
- **边框颜色**: 从 #E8E8E8 调整为 #EEEEEE
- **移除默认阴影**: 只在 hover 时显示阴影
- **文字样式**:
  - 项目名称: 从 18px 调整为 28px
  - 描述文字: 从 14px 调整为 24px，颜色从蓝色改为灰色 (#666666)

### 7. 图标系统升级
- **下载 Figma 图标**: 从设计稿中提取了 5 个高质量图标
  - `brain-icon.png`: 大脑图标
  - `iva-cpt-icon.png`: IVA-CPT 标准版图标
  - `iva-cpt-mini-icon.png`: IVA-CPT MINI 版图标
  - `ppvt-icon.png`: PPVT 测评图标
  - `questionnaire-icon.png`: 量表评估图标
- **CSS 背景图**: 使用背景图片替代文字图标
- **动态类名**: 添加 `getProjectIconClass` 方法区分不同项目类型

### 8. 响应式优化
- **字体大小统一**: 加载和空状态文字从 18px 调整为 28px
- **间距标准化**: 统一使用 32px 和 40px 的间距体系

## 技术实现

### 新增方法
```javascript
const getProjectIconClass = (quizType) => {
  switch (quizType) {
    case 'CPT-STD':
      return 'iva-cpt'
    case 'CPT-MINI':
      return 'iva-cpt-mini'
    case 'PPVT':
      return 'ppvt'
    default:
      return ''
  }
}
```

### CSS 类结构
```scss
.project-icon {
  &.test-icon {
    &.iva-cpt { /* IVA-CPT 标准版样式 */ }
    &.iva-cpt-mini { /* IVA-CPT MINI 版样式 */ }
    &.ppvt { /* PPVT 测评样式 */ }
  }
  &.quest-icon { /* 量表评估样式 */ }
}
```

## 文件更新

### 主要文件
- `pages/home/<USER>

### 新增资源
- `static/figma-assets/brain-icon.png`
- `static/figma-assets/iva-cpt-icon.png`
- `static/figma-assets/iva-cpt-mini-icon.png`
- `static/figma-assets/ppvt-icon.png`
- `static/figma-assets/questionnaire-icon.png`

## 设计原则

1. **视觉一致性**: 严格按照 Figma 设计稿的尺寸、颜色和布局
2. **用户体验**: 保持良好的交互反馈和视觉层次
3. **响应式设计**: 确保在不同屏幕尺寸下的良好显示
4. **性能优化**: 使用适当的图片格式和尺寸

## 兼容性

- ✅ 支持 uni-app 框架
- ✅ 兼容 H5、小程序、App 端
- ✅ 适配平板设备（主要目标设备）
- ✅ 保持原有功能逻辑不变

## 测试建议

1. 在不同设备上测试页面布局
2. 验证图标资源加载正常
3. 确认交互功能（点击、hover）正常
4. 检查在不同数据状态下的显示效果
