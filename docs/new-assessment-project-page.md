# 新测评项目页面实现说明

## 概述

新创建了一个测评项目页面 `pages/home/<USER>/home/<USER>

## 功能特性

### 1. 接口调用
- 调用接口：`project/v1.1/qryConceptualProjectList`
- 请求体：空对象 `{}`
- 自动解析返回的数据结构

### 2. 动态标签页切换
- 根据接口返回的数据显示孩子、家长、教师部分
- 参考 `pages/assessment/detail.vue` 的切换逻辑
- 支持点击切换不同部分

### 3. 项目显示布局

#### 孩子部分（respondentGroupType: 'C'）
- **分类显示**：根据 `quizType` 类型进行分类
- **测验类**（左侧列）：包含非 QUEST 类型的项目
  - `CPT-STD`：IVA-CPT标准版
  - `CPT-MINI`：IVA-CPT MINI
  - `PPVT`：PPVT测评
- **量表评估类**（右侧列）：包含 QUEST 类型的项目
- 每列垂直排列，长条形卡片显示

#### 家长部分（respondentGroupType: 'P'）和教师部分（respondentGroupType: 'T'）
- **统一显示**：所有项目都是 QUEST 类型
- **网格布局**：每行2个长条形卡片
- 不进行分类，按照统一样式显示

### 4. 项目卡片设计
- **长条形卡片**：包含图标、项目名称、项目类型
- **交互效果**：悬停时有阴影和位移效果
- **图标区分**：不同类型项目使用不同图标
  - 🧠：IVA-CPT 系列
  - 🎯：PPVT
  - 📋：量表评估

### 5. 页面跳转逻辑
根据 `quizType` 跳转到对应页面：
- `CPT-STD` → `/pages/ivacpt/introduce`
- `CPT-MINI` → `/pages/ivacpt/introduce?type=mini`
- `PPVT` → `/pages/introduce/index`
- `QUEST` → `/pages/scale/introduce`

## 技术实现

### 1. Vue 3 Composition API
使用 `<script setup>` 语法，包含：
- `ref`、`reactive`：响应式数据
- `computed`：计算属性
- `onMounted`、`onShow`：生命周期钩子

### 2. 响应式数据管理
```javascript
const loading = ref(false)
const activeTabIndex = ref(0)
const respondentGroups = ref([])
```

### 3. 计算属性
- `currentGroup`：当前选中的组
- `currentTabData`：当前标签页的数据
- `testProjects`：测验类项目（孩子部分）
- `questProjects`：量表评估项目（孩子部分）

### 4. 样式设计
- **响应式布局**：适配不同屏幕尺寸
- **现代化UI**：圆角、阴影、渐变效果
- **交互反馈**：悬停、点击状态
- **颜色主题**：与现有页面保持一致

## 数据结构适配

### 接口返回数据格式
```json
{
  "code": "0000",
  "msg": null,
  "data": [
    {
      "respondentGroupType": "C",
      "respondentGroupName": "孩子部分",
      "detailProjectInfoList": [
        {
          "projectId": "937595988623888384",
          "projectName": "儿童抑郁障碍目评量表（DSCRS）",
          "quizType": "QUEST",
          "categoryId": "0"
        }
      ]
    }
  ]
}
```

### 项目类型映射
- `QUEST`：量表评估
- `CPT-STD`：IVA-CPT标准版
- `CPT-MINI`：IVA-CPT MINI
- `PPVT`：PPVT测评

## 使用说明

1. **替换现有页面**：将 `new-index.vue` 重命名为 `index.vue` 替换现有页面
2. **接口配置**：确保接口 `project/v1.1/qryConceptualProjectList` 可正常访问
3. **路由配置**：无需修改路由配置，保持原有路径
4. **依赖检查**：确保 `@/utils/interceptors.js` 正常工作

## 兼容性说明

- 支持 uni-app 多端编译
- 兼容 Vue 3 语法
- 适配横屏显示（APP端）
- 响应式设计，支持不同分辨率

## 后续优化建议

1. **加载优化**：添加骨架屏或更丰富的加载状态
2. **错误处理**：完善网络错误和数据异常处理
3. **缓存机制**：添加数据缓存，减少重复请求
4. **动画效果**：添加页面切换和卡片展示动画
5. **无障碍支持**：添加键盘导航和屏幕阅读器支持
