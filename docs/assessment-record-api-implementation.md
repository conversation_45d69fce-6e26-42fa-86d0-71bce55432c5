# 测评记录页面API接口实现文档

## 概述

本文档描述了在测评记录页面 (`pages/record/assessment-record.vue`) 中实现的API接口请求功能。

## 实现的功能

### 1. 数据加载
- 页面进入时自动加载测评记录数据
- 支持分页加载，默认每页10条记录
- 显示加载状态和空数据状态

### 2. 搜索功能
- 支持按患者姓名/病案号搜索
- 搜索框支持回车键触发搜索
- 搜索时自动重置到第一页

### 3. 筛选功能
- **测评项目筛选**: 支持按测评项目类型筛选
- **性别筛选**: 支持按性别筛选 (M/F)
- **完成时间筛选**: 支持按完成日期筛选
- **来源筛选**: 支持按测评来源筛选 (测评工单/单独测评)
- **得分范围筛选**: 支持按最小/最大得分筛选
- **年龄范围筛选**: 支持按年龄范围筛选

### 4. 分页功能
- 支持上一页/下一页导航
- 支持页码直接跳转
- 支持输入页码跳转
- 显示总记录数和当前页信息

### 5. 数据显示
- 显示患者信息 (姓名、性别、年龄、病案号)
- 显示测评项目名称
- 显示完成时间
- 显示测评得分
- 显示测评来源
- 支持查看报告和再次测评操作

## API接口

### 接口地址
```
POST /question/v1.1/qryQuestionEvaluationListPage
```

### 请求参数格式

#### 基础请求 (无筛选条件)
```json
{
    "param": {
        "param": ""
    },
    "pageSize": 10,
    "pageIndex": 1
}
```

#### 完整请求 (包含所有筛选条件)
```json
{
    "param": {
        "param": "搜索关键词",
        "minScore": 50,
        "maxScore": 90,
        "evaluationSourceType": "1",
        "submitDate": "1755153224261",
        "sex": "F",
        "minAge": 5,
        "maxAge": 18,
        "projectType": "SNAP-IV"
    },
    "pageSize": 10,
    "pageIndex": 1
}
```

### 响应数据格式
```json
{
    "code": "0000",
    "msg": null,
    "data": [
        {
            "evaluatId": "941449001788641280",
            "workOrderId": "940680688171094016",
            "traineeId": "6687964ad9330ea428d926cb",
            "traineeSimDesc": "张智轩(男)/7岁2月，61945454",
            "projectId": "937595630895894528",
            "projectName": "SNAP-IV父母及教师评定量表（26项）",
            "createTime": "2025-08-13T06:31:33.000+00:00",
            "score": 36,
            "evaluationSourceType": "0",
            "state": "DONE"
        }
    ],
    "total": 2
}
```

## 数据处理

### 患者信息解析
系统会自动解析 `traineeSimDesc` 字段，提取以下信息：
- 患者姓名
- 性别 (男/女 -> M/F)
- 年龄 (岁)
- 月份
- 病案号

### 时间格式化
- 将ISO时间格式转换为本地时间显示
- 格式: YYYY-MM-DD HH:mm:ss

### 来源类型映射
- `evaluationSourceType: "0"` -> "测评工单"
- `evaluationSourceType: "1"` -> "单独测评"

## 文件修改

### 1. service/index.js
添加了新的API接口函数：
```javascript
export const qryAssessmentRecordList = (data) => {
    return httpRequest.post('question/v1.1/qryQuestionEvaluationListPage', data);
};
```

### 2. pages/record/assessment-record.vue
主要修改：
- 导入API接口函数
- 替换静态数据为动态API调用
- 实现数据加载、搜索、筛选、分页功能
- 添加加载状态和错误处理
- 实现数据格式化和显示

## 使用说明

1. **页面加载**: 进入页面时自动加载第一页数据
2. **搜索**: 在搜索框输入关键词，点击搜索按钮或按回车键
3. **筛选**: 使用各种筛选条件，点击搜索按钮应用筛选
4. **重置**: 点击重置按钮清空所有筛选条件并重新加载数据
5. **分页**: 使用分页控件浏览不同页面的数据

## 注意事项

1. 操作功能 (查看报告、再次测评) 暂未实现，仅显示提示信息
2. 接口地址需要根据实际后端服务进行调整
3. 错误处理会显示用户友好的提示信息
4. 所有筛选条件都是可选的，不填写则不传递给后端
