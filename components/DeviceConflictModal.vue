<template>
	<view class="device-conflict-modal" v-if="visible" @click="handleMaskClick">
		<view class="modal-content" @click.stop>
			<!-- 标题区域 -->
			<view class="title-section">
				<text class="title-text">提示</text>
			</view>
			
					<!-- 内容区域 -->
		<view class="content-section">
			<text class="content-text">发现有另一台设备已在执行该工单，是否确认到当前设备，另一台设备将自动退出。</text>
		</view>
		
		<!-- 密码输入区域 -->
		<view class="password-section" v-if="showPasswordInput">
			<view class="password-input-container">
				<input
					class="password-input"
					type="number"
					:value="password"
					@input="handlePasswordInput"
					@confirm="handleInputConfirm"
					placeholder="请输入登录密码后4位"
					maxlength="4"
					:focus="inputFocus"
				/>
			</view>
		</view>
		
		<!-- 错误提示 -->
		<view class="error-section" v-if="errorMessage">
			<text class="error-text">{{ errorMessage }}</text>
		</view>
		
		<!-- 按钮区域 -->
		<view class="button-section">
			<view class="button cancel-btn" @click="handleCancel">
				<text class="button-text">取消</text>
			</view>
			<view class="button confirm-btn" @click="handleConfirm" :class="{ disabled: showPasswordInput && !isPasswordValid }">
				<text class="button-text">{{ showPasswordInput ? '确认切换' : '确认切换' }}</text>
			</view>
		</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'DeviceConflictModal',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		loading: {
			type: Boolean,
			default: false
		}
	},
	emits: ['cancel', 'confirm', 'maskClick'],
	data() {
		return {
			showPasswordInput: false,
			password: '',
			errorMessage: '',
			inputFocus: false
		}
	},
	computed: {
		isPasswordValid() {
			return this.password && this.password.length === 4;
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				// 弹窗显示时重置状态
				this.showPasswordInput = false;
				this.password = '';
				this.errorMessage = '';
				this.inputFocus = false;
			}
		}
	},
	methods: {
		handleCancel() {
			this.$emit('cancel');
		},
		
		handleConfirm() {
			if (!this.showPasswordInput) {
				// 第一次点击确认，显示密码输入
				this.showPasswordInput = true;
				this.$nextTick(() => {
					this.inputFocus = true;
				});
			} else {
				// 第二次点击确认，验证密码
				if (!this.isPasswordValid) {
					this.showError('请输入4位数字密码');
					return;
				}
				
				if (this.loading) {
					return;
				}
				
				this.$emit('confirm', this.password);
			}
		},
		
		handlePasswordInput(e) {
			const value = e.detail.value;
			// 只允许输入4位数字
			if (value.length <= 4 && /^\d*$/.test(value)) {
				this.password = value;
				// 清除错误提示
				if (this.errorMessage) {
					this.errorMessage = '';
				}
			}
		},

		handleInputConfirm() {
			// 输入框确认时自动提交
			this.handleConfirm();
		},
		
		handleMaskClick() {
			// 点击遮罩层时触发
			this.$emit('maskClick');
		},
		
		showError(message) {
			this.errorMessage = message;
			// 3秒后自动清除错误提示
			setTimeout(() => {
				this.errorMessage = '';
			}, 3000);
		},
		
		// 外部调用显示错误
		setError(message) {
			this.showError(message);
		}
	}
}
</script>

<style lang="scss" scoped>
.device-conflict-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.modal-content {
	width: 640px;
	background: #FFFFFF;
	border-radius: 32px;
	padding: 40px;
	display: flex;
	flex-direction: column;
	justify-content: flex-end;
	gap: 56px;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.title-section {
	display: flex;
	justify-content: center;
	align-items: center;
	width: 560px;
	height: 48px;
}

.title-text {
	font-family: 'Alibaba PuHuiTi';
	font-weight: 400;
	font-size: 40px;
	line-height: 1.2em;
	text-align: center;
	color: #333333;
}

.content-section {
	width: 560px;
	height: 144px;
	display: flex;
	align-items: center;
}

.content-text {
	font-family: 'Alibaba PuHuiTi';
	font-weight: 400;
	font-size: 32px;
	line-height: 1.5em;
	text-align: justify;
	color: #666666;
	width: 100%;
}

.password-section {
	width: 560px;
	height: 115px;
	margin-bottom: 20px;
}

.password-input-container {
	width: 100%;
	height: 100%;
	background: #FFFFFF;
	border: 1px solid #C7C7C7;
	border-radius: 16px;
	display: flex;
	align-items: center;
	padding: 0 29px;
	box-sizing: border-box;
}

.password-input {
	width: 100%;
	height: 100%;
	border: none;
	outline: none;
	font-family: 'Alibaba PuHuiTi';
	font-weight: 400;
	font-size: 32px;
	color: #333333;
	background: transparent;
}

.password-input::placeholder {
	color: #999999;
	font-family: 'Alibaba PuHuiTi';
	font-weight: 400;
	font-size: 32px;
}

.error-section {
	min-height: 20px;
	margin-bottom: 10px;
	display: flex;
	align-items: center;
}

.error-text {
	font-family: 'Alibaba PuHuiTi';
	font-weight: 400;
	font-size: 24px;
	color: #FF4D50;
	line-height: 1.2em;
}

.button-section {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 560px;
	height: 88px;
	gap: 40px;
}

.button {
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 16px;
	cursor: pointer;
	transition: all 0.2s;
	flex: 1;
	height: 88px;
}

.cancel-btn {
	background: #55CC66;
	border: 2px solid #287FFF;
	
	.button-text {
		color: #287FFF;
	}
	
	&:hover {
		background: #4AB85C;
	}
	
	&:active {
		background: #3FA050;
	}
}

.confirm-btn {
	background: #287FFF;
	
	.button-text {
		color: #FFFFFF;
	}
	
	&:hover:not(.disabled) {
		background: #1E6FE6;
	}
	
	&:active:not(.disabled) {
		background: #1A5FCC;
	}
	
	&.disabled {
		background: #CCCCCC;
		cursor: not-allowed;
		
		.button-text {
			color: #999999;
		}
	}
}

.button-text {
	font-family: 'Alibaba PuHuiTi';
	font-weight: 400;
	font-size: 32px;
	line-height: 1.25em;
	text-align: center;
}

/* 响应式适配 */
@media screen and (max-width: 768px) {
	.modal-content {
		width: 90%;
		max-width: 640px;
		padding: 30px;
		gap: 40px;
	}
	
	.title-section,
	.content-section,
	.button-section {
		width: 100%;
	}
	
	.title-text {
		font-size: 36px;
	}
	
	.content-text {
		font-size: 28px;
	}
	
	.button-text {
		font-size: 28px;
	}
	
	.button-section {
		gap: 20px;
		height: 80px;
	}
	
	.button {
		height: 80px;
	}
}
</style>
