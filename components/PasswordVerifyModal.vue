<template>
	<view class="password-verify-modal" v-if="visible" @click="handleMaskClick">
		<view class="modal-content" @click.stop>
			<!-- 内容区域 -->
			<view class="content-section">
				<text class="content-text">该工单已在另一台设备执行，请输入登录密码后4位，确认当前设备退出该工单</text>
			</view>
			
			<!-- 密码输入区域 -->
			<view class="password-section">
				<view class="password-input-container">
					<input
						class="password-input"
						type="number"
						:value="password"
						@input="handlePasswordInput"
						@confirm="handleInputConfirm"
						placeholder="请输入登录密码后4位"
						maxlength="4"
						:focus="inputFocus"
					/>
				</view>
			</view>
			
			<!-- 错误提示 -->
			<view class="error-section" v-if="errorMessage">
				<text class="error-text">{{ errorMessage }}</text>
			</view>
			
			<!-- 按钮区域 -->
			<view class="button-section">
				<view class="button confirm-btn" @click="handleConfirm" :class="{ disabled: !isPasswordValid }">
					<text class="button-text">确认退出</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'PasswordVerifyModal',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		loading: {
			type: Boolean,
			default: false
		}
	},
	emits: ['confirm', 'maskClick'],
	data() {
		return {
			password: '',
			errorMessage: '',
			inputFocus: false
		}
	},
	computed: {
		isPasswordValid() {
			return this.password && this.password.length === 4;
		}
	},
	watch: {
		visible(newVal) {
			if (newVal) {
				// 弹窗显示时清空之前的数据并聚焦输入框
				this.password = '';
				this.errorMessage = '';
				this.$nextTick(() => {
					this.inputFocus = true;
				});
			} else {
				this.inputFocus = false;
			}
		}
	},
	methods: {
		handlePasswordInput(e) {
			const value = e.detail.value;
			console.log('PasswordVerifyModal - 密码输入:', value);
			// 只允许输入4位数字
			if (value.length <= 4 && /^\d*$/.test(value)) {
				this.password = value;
				// 清除错误提示
				if (this.errorMessage) {
					this.errorMessage = '';
				}
			}
		},

		handleInputConfirm() {
			console.log('PasswordVerifyModal - 输入框确认事件被触发');
			// 可以选择是否在输入完成后自动提交，这里先记录日志
			// this.handleConfirm();
		},

		handleConfirm() {
			console.log('PasswordVerifyModal - handleConfirm 被调用，密码:', this.password);
			console.log('PasswordVerifyModal - 当前状态:', {
				visible: this.visible,
				loading: this.loading,
				isPasswordValid: this.isPasswordValid
			});

			if (!this.visible) {
				console.log('PasswordVerifyModal - 弹窗未显示，忽略确认操作');
				return;
			}

			if (!this.isPasswordValid) {
				this.showError('请输入4位数字密码');
				return;
			}

			if (this.loading) {
				console.log('PasswordVerifyModal - 正在加载中，忽略重复操作');
				return;
			}

			console.log('PasswordVerifyModal - 发送确认事件，密码:', this.password);
			this.$emit('confirm', this.password);
		},
		
		handleMaskClick() {
			// 点击遮罩层时触发
			this.$emit('maskClick');
		},
		
		showError(message) {
			this.errorMessage = message;
			// 3秒后自动清除错误提示
			setTimeout(() => {
				this.errorMessage = '';
			}, 3000);
		},
		
		// 外部调用显示错误
		setError(message) {
			this.showError(message);
		}
	}
}
</script>

<style lang="scss" scoped>
.password-verify-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.modal-content {
	width: 640px;
	height: 472px;
	background: #FFFFFF;
	border-radius: 32px;
	padding: 40px;
	display: flex;
	flex-direction: column;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
	position: relative;
}

.content-section {
	width: 567px;
	height: 96px;
	margin-bottom: 22px;
}

.content-text {
	font-family: 'Alibaba PuHuiTi';
	font-weight: 400;
	font-size: 32px;
	line-height: 1.5em;
	text-align: justify;
	color: #666666;
}

.password-section {
	width: 560px;
	height: 115px;
	margin-bottom: 20px;
}

.password-input-container {
	width: 100%;
	height: 100%;
	background: #FFFFFF;
	border: 1px solid #C7C7C7;
	border-radius: 16px;
	display: flex;
	align-items: center;
	padding: 0 29px;
	box-sizing: border-box;
}

.password-input {
	width: 100%;
	height: 100%;
	border: none;
	outline: none;
	font-family: 'Alibaba PuHuiTi';
	font-weight: 400;
	font-size: 32px;
	color: #333333;
	background: transparent;
}

.password-input::placeholder {
	color: #999999;
	font-family: 'Alibaba PuHuiTi';
	font-weight: 400;
	font-size: 32px;
}

.error-section {
	min-height: 20px;
	margin-bottom: 10px;
	display: flex;
	align-items: center;
}

.error-text {
	font-family: 'Alibaba PuHuiTi';
	font-weight: 400;
	font-size: 24px;
	color: #FF4D50;
	line-height: 1.2em;
}

.button-section {
	width: 560px;
	height: 88px;
	margin-top: auto;
}

.button {
	width: 100%;
	height: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 16px;
	cursor: pointer;
	transition: all 0.2s;
}

.confirm-btn {
	background: #287FFF;
	
	.button-text {
		color: #FFFFFF;
	}
	
	&:hover:not(.disabled) {
		background: #1E6FE6;
	}
	
	&:active:not(.disabled) {
		background: #1A5FCC;
	}
	
	&.disabled {
		background: #CCCCCC;
		cursor: not-allowed;
		
		.button-text {
			color: #999999;
		}
	}
}

.button-text {
	font-family: 'Alibaba PuHuiTi';
	font-weight: 400;
	font-size: 32px;
	line-height: 1.25em;
	text-align: center;
}

/* 响应式适配 */
@media screen and (max-width: 768px) {
	.modal-content {
		width: 90%;
		max-width: 640px;
		height: auto;
		min-height: 400px;
		padding: 30px;
	}
	
	.content-section,
	.password-section,
	.button-section {
		width: 100%;
	}
	
	.content-text {
		font-size: 28px;
	}
	
	.password-input,
	.password-input::placeholder {
		font-size: 28px;
	}
	
	.button-text {
		font-size: 28px;
	}
	
	.error-text {
		font-size: 22px;
	}
}
</style>
