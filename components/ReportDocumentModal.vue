<template>
	<view class="report-modal" v-if="visible" @click="closeModal">
		<view class="report-modal-content" @click.stop>
			<view class="modal-header">
				<text class="modal-title">选择报告格式</text>
			</view>
			
			<view class="document-list">
				<view class="document-item" v-if="reportData.pdf" @click="openDocument('pdf')">
					<view class="document-icon pdf-icon"></view>
					<text class="document-text">PDF 报告</text>
					<view class="arrow-icon"></view>
				</view>
				
				<view class="document-item" v-if="reportData.doc" @click="openDocument('doc')">
					<view class="document-icon doc-icon"></view>
					<text class="document-text">Word 报告</text>
					<view class="arrow-icon"></view>
				</view>
			</view>
			
			<view class="modal-footer">
				<view class="cancel-btn" @click="closeModal">
					<text class="cancel-text">取消</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
export default {
	name: 'ReportDocumentModal',
	props: {
		visible: {
			type: Boolean,
			default: false
		},
		reportData: {
			type: Object,
			default: () => ({})
		}
	},
	methods: {
		closeModal() {
			this.$emit('close');
		},
		
		openDocument(type) {
			const url = type === 'pdf' ? this.reportData.pdf : this.reportData.doc;
			if (url) {
				this.$emit('openDocument', { type, url });
			}
			this.closeModal();
		}
	}
}
</script>

<style lang="scss" scoped>
.report-modal {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	display: flex;
	justify-content: center;
	align-items: center;
	z-index: 9999;
}

.report-modal-content {
	width: 400px;
	background: #FFFFFF;
	border-radius: 22px;
	padding: 0;
	box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
	overflow: hidden;
}

.modal-header {
	padding: 28px 28px 20px;
	border-bottom: 1px solid #EEEEEE;
	
	.modal-title {
		font-family: 'Alibaba PuHuiTi';
		font-size: 24px;
		font-weight: 500;
		color: #333333;
		text-align: center;
		width: 100%;
	}
}

.document-list {
	padding: 20px 0;
}

.document-item {
	display: flex;
	align-items: center;
	padding: 16px 28px;
	cursor: pointer;
	transition: background-color 0.2s;
	
	&:hover {
		background: #F8F9FA;
	}
	
	.document-icon {
		width: 32px;
		height: 32px;
		margin-right: 16px;
		border-radius: 4px;
		
		&.pdf-icon {
			background: #FF4D4F;
			position: relative;
			
			&::after {
				content: 'PDF';
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: #FFFFFF;
				font-size: 8px;
				font-weight: bold;
				font-family: 'Alibaba PuHuiTi';
			}
		}
		
		&.doc-icon {
			background: #1890FF;
			position: relative;
			
			&::after {
				content: 'DOC';
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				color: #FFFFFF;
				font-size: 8px;
				font-weight: bold;
				font-family: 'Alibaba PuHuiTi';
			}
		}
	}
	
	.document-text {
		flex: 1;
		font-family: 'Alibaba PuHuiTi';
		font-size: 18px;
		color: #333333;
		font-weight: 400;
	}
	
	.arrow-icon {
		width: 16px;
		height: 16px;
		border-right: 2px solid #CCCCCC;
		border-bottom: 2px solid #CCCCCC;
		transform: rotate(-45deg);
	}
}

.modal-footer {
	padding: 20px 28px 28px;
	border-top: 1px solid #EEEEEE;
	
	.cancel-btn {
		width: 100%;
		height: 44px;
		background: #FFFFFF;
		border: 1px solid #287FFF;
		border-radius: 11px;
		display: flex;
		justify-content: center;
		align-items: center;
		cursor: pointer;
		transition: background-color 0.2s;
		
		&:hover {
			background: #F3F8FF;
		}
		
		.cancel-text {
			font-family: 'Alibaba PuHuiTi';
			font-size: 18px;
			color: #287FFF;
			font-weight: 400;
		}
	}
}
</style>
