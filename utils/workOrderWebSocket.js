// @/utils/workOrderWebSocket.js
import { isJSON } from "@/common/method.js"
import { getStorageSync } from "../common/uniTool";

// 注意：参数现在通过header传递，不再需要构建查询参数字符串

// WebSocket消息类型常量
export const MESSAGE_TYPES = {
    OPEN_ORDER_REQUEST: "OPEN_ORDER_REQUEST",        // 请求打开工单
    CAN_OPEN_ORDER: "CAN_OPEN_ORDER",                // 可以打开工单
    NEED_CONFIRM_SWITCH: "NEED_CONFIRM_SWITCH",      // 需要确认切换工单
    CLOSE_ORDER: "CLOSE_ORDER",                      // 强制切换设备
    REQUIRE_EXIT: "REQUIRE_EXIT",                    // 发现有其他设备已在执行该工单，请立刻退出
    ACTIVE_EXIT: "ACTIVE_EXIT",                      // 主动退出
    ACTIVE_EXIT_SUCCESS: "ACTIVE_EXIT_SUCCESS"       // 已成功退出工单
};

/**
 * 工单WebSocket连接管理类
 */
class WorkOrderWebSocketClass {
    constructor() {
        this.lockReconnect = false; // 是否开始重连
        this.wsUrl = ""; // ws 地址
        this.globalCallback = null; // 回调方法
        this.userClose = false; // 是否主动关闭
        this.ws = null; // WebSocket实例
        this.connectionParams = null; // 连接参数
        this.isConnected = false; // 连接状态
        this.reconnectAttempts = 0; // 重连次数
        this.maxReconnectAttempts = 3; // 最大重连次数（减少重连次数）
        this.baseReconnectDelay = 3000; // 基础重连延迟（增加到3秒）
        this.maxReconnectDelay = 30000; // 最大重连延迟（30秒）
    }

    /**
     * 创建WebSocket连接
     * @param {string} baseUrl - WebSocket基础URL（将被转换为workOrderLink地址）
     * @param {string} token - 请求token
     * @param {string} deviceId - 设备Id(或者设备ip)
     * @param {string} workOrderId - 工单Id
     * @param {string} sourceType - 参数来源（值为PAD）
     */
    createWebSocket(baseUrl, token, deviceId, workOrderId, sourceType = 'PAD') {
        if (typeof (uni.connectSocket) === 'undefined') {
            this.writeToScreen("您的浏览器不支持WebSocket，无法获取数据");
            return false;
        }

        // 将deviceCctLink地址转换为workOrderLink地址
        let workOrderBaseUrl = baseUrl;
        if (baseUrl.includes('deviceCctLink')) {
            workOrderBaseUrl = baseUrl.replace('deviceCctLink', 'workOrderLink');
        } else if (baseUrl.endsWith('/')) {
            workOrderBaseUrl = baseUrl + 'workOrderLink';
        } else {
            workOrderBaseUrl = baseUrl + '/workOrderLink';
        }
        
        // 使用纯净的WebSocket URL，不添加查询参数
        this.wsUrl = workOrderBaseUrl;
        
        // 构建header参数
        const headers = {
            'token': token || '',
            'deviceId': deviceId || '',
            'sourceType': sourceType || 'PAD'
        };
        
        // 只有当workOrderId不为空时才添加到header中
        if (workOrderId && workOrderId.trim() !== '') {
            headers['workOrderId'] = workOrderId;
        }
        
        console.log('WorkOrder WebSocket连接URL:', this.wsUrl);
        console.log('WorkOrder WebSocket连接参数（通过header传递）:', {
            baseUrl,
            token: token ? `${token.substring(0, 10)}...` : 'empty',
            deviceId,
            workOrderId: workOrderId || 'empty',
            sourceType,
            headers: headers
        });
        
        // 打印header参数的完整内容（用于调试）
        console.log('=== WebSocket Header参数完整内容 ===');
        console.log('Header对象:', JSON.stringify(headers, null, 2));
        Object.keys(headers).forEach(key => {
            const value = headers[key];
            console.log(`Header[${key}]:`, {
                type: typeof value,
                length: value ? value.length : 0,
                value: key === 'token' ? (value ? `${value.substring(0, 20)}...` : 'empty') : value,
                isEmpty: !value || value.trim() === ''
            });
        });
        console.log('=== Header参数完整内容结束 ===');
        
        // 保存连接参数用于重连
        this.connectionParams = {
            baseUrl,
            token,
            deviceId,
            workOrderId,
            sourceType,
            headers
        };

        try {
            // 创建WebSocket连接
            // #ifdef H5
            // H5环境下，WebSocket构造函数不支持header参数，需要在连接建立后通过消息发送参数
            this.ws = new WebSocket(this.wsUrl);
            this.initEventHandle();
            // #endif

            // #ifdef APP-PLUS
            this.ws = uni.connectSocket({
                url: this.wsUrl,
                header: headers, // 在APP-PLUS环境下通过header传递参数
                success: (data) => {
                    console.log('WorkOrder WebSocket连接请求发送成功（带header）:', data);
                    // 注意：success只表示连接请求发送成功，不代表连接已建立
                    // 真正的连接建立在onOpen事件中处理
                    this.initEventHandle();
                },
                fail: (e) => {
                    console.log('WorkOrder WebSocket连接请求失败:', e);
                    this.isConnected = false;
                    this.reconnect();
                }
            });
            // #endif
        } catch (e) {
            console.log('WorkOrder WebSocket创建异常:', e);
            this.reconnect();
        }
    }

    /**
     * 初始化事件处理
     */
    initEventHandle() {
        if (!this.ws) return;

        // #ifdef H5
        this.ws.onopen = (event) => {
            console.log("WorkOrder WebSocket连接打开", {
                url: this.wsUrl,
                readyState: this.ws.readyState
            });
            // 添加短暂延迟确保连接稳定
            setTimeout(() => {
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.lockReconnect = false; // 重置重连锁
                console.log('WorkOrder WebSocket连接状态已稳定');
                // H5环境下需要手动发送连接参数作为认证消息
                if (this.connectionParams && this.connectionParams.headers) {
                    const authMessage = {
                        type: 'AUTH',
                        ...this.connectionParams.headers
                    };
                    console.log('H5环境发送认证消息:', authMessage);
                    
                    // 打印认证消息的完整内容
                    console.log('=== H5认证消息完整内容 ===');
                    console.log('认证消息对象:', JSON.stringify(authMessage, null, 2));
                    Object.keys(authMessage).forEach(key => {
                        const value = authMessage[key];
                        console.log(`AuthMessage[${key}]:`, {
                            type: typeof value,
                            length: value ? value.length : 0,
                            value: key === 'token' ? (value ? `${value.substring(0, 20)}...` : 'empty') : value,
                            isEmpty: !value || value.trim() === ''
                        });
                    });
                    console.log('=== H5认证消息完整内容结束 ===');
                    
                    this.ws.send(JSON.stringify(authMessage));
                }
            }, 100);
        };

        this.ws.onclose = (event) => {
            console.log("WorkOrder WebSocket连接关闭", {
                code: event.code,
                reason: event.reason,
                wasClean: event.wasClean,
                url: this.wsUrl
            });
            this.isConnected = false;
            if (!this.userClose) {
                this.reconnect();
            }
        };

        this.ws.onerror = (event) => {
            console.log("WorkOrder WebSocket连接错误", {
                error: event,
                url: this.wsUrl,
                readyState: this.ws ? this.ws.readyState : 'no ws',
                reconnectAttempts: this.reconnectAttempts,
                lockReconnect: this.lockReconnect
            });
            this.isConnected = false;
            // 添加防抖逻辑，避免错误事件和关闭事件同时触发重连
            if (!this.userClose && !this.lockReconnect) {
                setTimeout(() => {
                    if (!this.isConnected && !this.userClose) {
                        this.reconnect();
                    }
                }, 50);
            }
        };

        this.ws.onmessage = (event) => {
            this.handleMessage(event.data);
        };
        // #endif

        // #ifdef APP-PLUS
        this.ws.onOpen(res => {
            console.log('WorkOrder WebSocket连接打开:', {
                result: res,
                url: this.wsUrl
            });
            
            // 打印APP-PLUS环境下使用的header参数
            if (this.connectionParams && this.connectionParams.headers) {
                console.log('=== APP-PLUS环境Header参数确认 ===');
                console.log('连接成功时的Header参数:', JSON.stringify(this.connectionParams.headers, null, 2));
                Object.keys(this.connectionParams.headers).forEach(key => {
                    const value = this.connectionParams.headers[key];
                    console.log(`APP-Header[${key}]:`, {
                        type: typeof value,
                        length: value ? value.length : 0,
                        value: key === 'token' ? (value ? `${value.substring(0, 20)}...` : 'empty') : value,
                        isEmpty: !value || value.trim() === ''
                    });
                });
                console.log('=== APP-PLUS环境Header参数确认结束 ===');
            }
            
            // 添加短暂延迟确保连接稳定
            setTimeout(() => {
                this.isConnected = true;
                this.reconnectAttempts = 0;
                this.lockReconnect = false; // 重置重连锁
                console.log('WorkOrder WebSocket连接状态已稳定');
            }, 100);
        });

        this.ws.onClose((err) => {
            console.log('WorkOrder WebSocket连接关闭:', {
                error: err,
                url: this.wsUrl,
                userClose: this.userClose
            });
            this.isConnected = false;
            if (!this.userClose) {
                this.reconnect();
            }
        });

        this.ws.onError((err) => {
            console.log('WorkOrder WebSocket连接错误:', {
                error: err,
                url: this.wsUrl,
                isConnected: this.isConnected,
                reconnectAttempts: this.reconnectAttempts,
                lockReconnect: this.lockReconnect
            });
            this.isConnected = false;
            // 添加防抖逻辑，避免错误事件和关闭事件同时触发重连
            if (!this.userClose && !this.lockReconnect) {
                setTimeout(() => {
                    if (!this.isConnected && !this.userClose) {
                        this.reconnect();
                    }
                }, 50);
            }
        });

        this.ws.onMessage(event => {
            this.handleMessage(event.data);
        });
        // #endif
    }

    /**
     * 处理接收到的消息
     * @param {string} data - 消息数据
     */
    handleMessage(data) {
        try {
            let messageData;
            if (isJSON(data)) {
                messageData = JSON.parse(data);
            } else {
                messageData = data;
            }
            
            console.log('WorkOrder WebSocket收到消息:', messageData);
            
            // 调用全局回调
            if (this.globalCallback) {
                this.globalCallback(messageData);
            }
        } catch (error) {
            console.error('WorkOrder WebSocket消息处理错误:', error);
        }
    }

    /**
     * 重连机制（使用指数退避算法）
     */
    reconnect() {
        if (this.lockReconnect || this.userClose) return;
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('WorkOrder WebSocket重连次数超限，停止重连');
            return;
        }

        this.lockReconnect = true;
        this.reconnectAttempts++;

        // 计算重连延迟（指数退避）
        const delay = Math.min(
            this.baseReconnectDelay * Math.pow(2, this.reconnectAttempts - 1),
            this.maxReconnectDelay
        );

        console.log(`WorkOrder WebSocket开始第${this.reconnectAttempts}次重连，延迟${delay}ms`);

        if (this.ws) {
            try {
                this.ws.close();
            } catch (e) {
                console.log('关闭旧连接失败:', e);
            }
        }

        setTimeout(() => {
            this.lockReconnect = false; // 在重连前重置锁
            if (this.connectionParams && !this.userClose) {
                const { baseUrl, token, deviceId, workOrderId, sourceType } = this.connectionParams;
                console.log(`执行第${this.reconnectAttempts}次重连，工单ID: ${workOrderId}`);
                this.createWebSocket(baseUrl, token, deviceId, workOrderId, sourceType);
            }
        }, delay);
    }

    /**
     * 发送消息
     * @param {Object} messageData - 消息数据
     * @param {string} messageData.type - 消息类型
     * @param {string} messageData.workOrderId - 工单Id
     * @param {string} messageData.message - 消息内容（可选，默认为空字符串）
     * @param {string} messageData.passwordLast4 - 密码后四位（可选，默认为空字符串）
     */
    sendMessage(messageData) {
        if (!this.ws || !this.isConnected) {
            console.error('WorkOrder WebSocket未连接，无法发送消息');
            return false;
        }

        // 确保消息格式完整，所有字段都存在
        const completeMessage = {
            type: messageData.type || '',
            workOrderId: messageData.workOrderId || '',
            message: messageData.message || '',
            passwordLast4: messageData.passwordLast4 || ''
        };

        const message = JSON.stringify(completeMessage);
        console.log('WorkOrder WebSocket发送消息:', message);

        try {
            // #ifdef H5
            this.ws.send(message);
            // #endif

            // #ifdef APP-PLUS
            this.ws.send({
                data: message,
                success: () => {
                    console.log("WorkOrder WebSocket消息发送成功");
                },
                fail: (err) => {
                    console.log("WorkOrder WebSocket消息发送失败:", err);
                }
            });
            // #endif
            return true;
        } catch (error) {
            console.error('WorkOrder WebSocket发送消息异常:', error);
            return false;
        }
    }

    /**
     * 设置消息回调
     * @param {Function} callback - 回调函数
     */
    setMessageCallback(callback) {
        this.globalCallback = callback;
    }

    /**
     * 关闭WebSocket连接
     */
    closeSocket() {
        if (this.ws) {
            this.userClose = true;
            this.isConnected = false;
            
            // #ifdef H5
            this.ws.close();
            // #endif

            // #ifdef APP-PLUS
            this.ws.close({
                success: (res) => {
                    console.log("WorkOrder WebSocket关闭成功", res);
                },
                fail: (err) => {
                    console.log("WorkOrder WebSocket关闭失败", err);
                }
            });
            // #endif
        }
    }

    /**
     * 获取连接状态
     */
    getConnectionStatus() {
        return {
            isConnected: this.isConnected,
            reconnectAttempts: this.reconnectAttempts,
            wsUrl: this.wsUrl,
            readyState: this.ws ? this.ws.readyState : 'no ws',
            lockReconnect: this.lockReconnect,
            userClose: this.userClose
        };
    }

    /**
     * 测试简单连接（用于调试）
     * @param {string} baseUrl - 基础URL
     * @param {Object} testHeaders - 测试用的header参数（可选）
     */
    testSimpleConnection(baseUrl, testHeaders = null) {
        // 将deviceCctLink地址转换为workOrderLink地址
        let workOrderBaseUrl = baseUrl;
        if (baseUrl.includes('deviceCctLink')) {
            workOrderBaseUrl = baseUrl.replace('deviceCctLink', 'workOrderLink');
        } else if (baseUrl.endsWith('/')) {
            workOrderBaseUrl = baseUrl + 'workOrderLink';
        } else {
            workOrderBaseUrl = baseUrl + '/workOrderLink';
        }
        
        console.log('测试简单WebSocket连接:', workOrderBaseUrl);
        if (testHeaders) {
            console.log('测试header参数:', testHeaders);
            
            // 打印测试header参数的完整内容
            console.log('=== 测试连接Header参数完整内容 ===');
            console.log('测试Header对象:', JSON.stringify(testHeaders, null, 2));
            Object.keys(testHeaders).forEach(key => {
                const value = testHeaders[key];
                console.log(`TestHeader[${key}]:`, {
                    type: typeof value,
                    length: value ? value.length : 0,
                    value: key === 'token' ? (value ? `${value.substring(0, 20)}...` : 'empty') : value,
                    isEmpty: !value || value.trim() === ''
                });
            });
            console.log('=== 测试连接Header参数完整内容结束 ===');
        }
        
        try {
            // #ifdef H5
            const testWs = new WebSocket(workOrderBaseUrl);
            testWs.onopen = () => {
                console.log('简单连接成功');
                // H5环境下如果有测试header，发送认证消息
                if (testHeaders) {
                    const authMessage = { type: 'AUTH', ...testHeaders };
                    testWs.send(JSON.stringify(authMessage));
                }
                setTimeout(() => testWs.close(), 1000);
            };
            testWs.onerror = (err) => {
                console.log('简单连接失败:', err);
            };
            testWs.onclose = (event) => {
                console.log('简单连接关闭:', event.code, event.reason);
            };
            // #endif
            
            // #ifdef APP-PLUS
            const connectOptions = {
                url: workOrderBaseUrl,
                success: () => {
                    console.log('简单连接成功');
                },
                fail: (err) => {
                    console.log('简单连接失败:', err);
                }
            };
            
            // 如果有测试header，添加到连接选项中
            if (testHeaders) {
                connectOptions.header = testHeaders;
            }
            
            const testWs = uni.connectSocket(connectOptions);
            // #endif
        } catch (error) {
            console.log('简单连接异常:', error);
        }
    }

    /**
     * 输出日志
     * @param {string} message - 日志消息
     */
    writeToScreen(message) {
        console.log('WorkOrder WebSocket:', message);
    }
}

export default WorkOrderWebSocketClass;
