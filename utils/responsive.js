/**
 * 响应式适配工具
 * <AUTHOR>
 * @description 基于屏幕尺寸和像素比的动态适配工具
 */

// 设计稿基准尺寸（920x1080，15.6英寸屏幕）
const DESIGN_WIDTH = 920;
const DESIGN_HEIGHT = 1080;

/**
 * 获取当前设备信息
 */
export function getDeviceInfo() {
    return new Promise((resolve) => {
        // #ifdef H5
        const info = {
            windowWidth: window.innerWidth,
            windowHeight: window.innerHeight,
            pixelRatio: window.devicePixelRatio || 1,
            screenWidth: window.screen.width,
            screenHeight: window.screen.height
        };
        resolve(info);
        // #endif
        
        // #ifdef APP-PLUS
        uni.getSystemInfo({
            success: (res) => {
                resolve({
                    windowWidth: res.windowWidth,
                    windowHeight: res.windowHeight,
                    pixelRatio: res.pixelRatio || 1,
                    screenWidth: res.screenWidth,
                    screenHeight: res.screenHeight
                });
            }
        });
        // #endif
    });
}

/**
 * 检测是否为11英寸高分辨率屏幕
 * @param {Object} deviceInfo 设备信息
 * @returns {boolean} 是否为11英寸屏幕
 */
export function is11InchScreen(deviceInfo) {
    const { windowWidth, windowHeight, pixelRatio } = deviceInfo;

    // 计算物理分辨率（实际像素）
    const physicalPixelWidth = windowWidth * pixelRatio;
    const physicalPixelHeight = windowHeight * pixelRatio;

    // 检测目标物理分辨率：1920×1200 (允许更大误差范围)
    const isTargetPhysicalResolution =
        Math.abs(physicalPixelWidth - 1920) <= 30 &&
        Math.abs(physicalPixelHeight - 1200) <= 50; // 1173到1200差27像素，放宽到50

    // 检测逻辑分辨率范围：约1097×670 (允许一定误差)
    const isTargetLogicalResolution =
        windowWidth >= 1000 && windowWidth <= 1200 &&
        windowHeight >= 600 && windowHeight <= 750;

    // 检测像素密度比：约1.75 (允许1.5-2.0范围)
    const isTargetPixelRatio = pixelRatio >= 1.5 && pixelRatio <= 2.0;

    // 计算物理尺寸（英寸）- 基于物理像素
    const physicalWidth = physicalPixelWidth / (96 * pixelRatio);
    const physicalHeight = physicalPixelHeight / (96 * pixelRatio);
    const diagonalInches = Math.sqrt(physicalWidth * physicalWidth + physicalHeight * physicalHeight);

    // 11英寸屏幕的对角线尺寸范围
    const is11InchSize = diagonalInches >= 10.5 && diagonalInches <= 12.5;

    // 主要检测：物理分辨率 + 像素密度比
    const primaryDetection = isTargetPhysicalResolution && isTargetPixelRatio;

    // 备用检测：逻辑分辨率 + 像素密度比 + 物理尺寸
    const fallbackDetection = isTargetLogicalResolution && isTargetPixelRatio && is11InchSize;

    const result = primaryDetection || fallbackDetection;

    // 详细调试信息
    console.log('🔍 11英寸屏幕检测详情:');
    console.log('📱 设备参数:', {
        逻辑像素: `${windowWidth}×${windowHeight}`,
        物理像素: `${physicalPixelWidth.toFixed(0)}×${physicalPixelHeight.toFixed(0)}`,
        像素密度比: pixelRatio,
        物理尺寸: `${physicalWidth.toFixed(2)}"×${physicalHeight.toFixed(2)}"`,
        对角线: `${diagonalInches.toFixed(2)}"`
    });
    console.log('✅ 检测条件:', {
        物理分辨率匹配: isTargetPhysicalResolution,
        逻辑分辨率匹配: isTargetLogicalResolution,
        像素密度比匹配: isTargetPixelRatio,
        物理尺寸匹配: is11InchSize
    });
    console.log('🎯 检测结果:', {
        主要检测: primaryDetection,
        备用检测: fallbackDetection,
        最终结果: result ? '✅ 是11英寸屏幕' : '❌ 不是11英寸屏幕'
    });

    return result;
}

/**
 * 计算缩放比例
 * @param {Object} deviceInfo 设备信息
 * @returns {Object} 缩放比例信息
 */
export function calculateScale(deviceInfo) {
    const { windowWidth, windowHeight, pixelRatio } = deviceInfo;
    
    // 计算物理尺寸（英寸）
    const physicalWidth = windowWidth / (96 * pixelRatio); // 96 DPI 标准
    const physicalHeight = windowHeight / (96 * pixelRatio);
    const diagonalInches = Math.sqrt(physicalWidth * physicalWidth + physicalHeight * physicalHeight);
    
    // 检测11英寸屏幕
    const is11Inch = is11InchScreen(deviceInfo);
    
    // 基于屏幕尺寸的缩放因子
    let sizeScale = 1;
    if (is11Inch) {
        // 11英寸屏幕专用缩放
        sizeScale = 0.75; // 更激进的缩放以优化显示
    } else if (diagonalInches < 13) {
        // 其他小屏幕设备，缩小显示
        sizeScale = 0.75 + (diagonalInches - 10) * 0.083; // 10英寸=0.75, 13英寸=1.0
    } else if (diagonalInches > 17) {
        // 大屏幕设备，放大显示
        sizeScale = 1 + (diagonalInches - 17) * 0.05; // 17英寸=1.0, 每增加1英寸+0.05
    }
    
    // 基于分辨率的缩放因子
    const widthScale = windowWidth / DESIGN_WIDTH;
    const heightScale = windowHeight / DESIGN_HEIGHT;
    const resolutionScale = Math.min(widthScale, heightScale);
    
    // 基于像素比的调整
    const dpiScale = Math.min(pixelRatio / 1.5, 1.2); // 限制最大1.2倍
    
    // 综合缩放比例
    let finalScale;
    if (is11Inch) {
        // 11英寸屏幕使用优化的缩放比例
        // 基于实际测试调整，确保显示效果最佳
        finalScale = 0.85; // 从0.75调整到0.85，避免过度缩小
        console.log('🎯 11英寸屏幕缩放:', {
            检测结果: '✅ 11英寸屏幕',
            缩放比例: finalScale,
            说明: '应用专用优化'
        });
    } else {
        finalScale = sizeScale * resolutionScale * (1 / dpiScale);
        finalScale = Math.max(0.6, Math.min(1.4, finalScale)); // 限制在0.6-1.4倍之间
        console.log('📐 普通屏幕缩放:', {
            检测结果: '❌ 普通屏幕',
            缩放比例: finalScale,
            计算过程: `${sizeScale.toFixed(3)} × ${resolutionScale.toFixed(3)} × ${(1/dpiScale).toFixed(3)} = ${finalScale.toFixed(3)}`
        });
    }
    
    return {
        sizeScale,
        resolutionScale,
        dpiScale,
        finalScale,
        is11InchScreen: is11Inch,
        deviceInfo: {
            ...deviceInfo,
            physicalWidth,
            physicalHeight,
            diagonalInches
        }
    };
}

/**
 * 转换px值为适配后的值
 * @param {number} px 原始px值
 * @param {number} scale 缩放比例
 * @returns {string} 适配后的CSS值
 */
export function adaptPx(px, scale = 1) {
    return Math.round(px * scale) + 'px';
}

/**
 * 转换字体大小
 * @param {number} fontSize 原始字体大小
 * @param {number} scale 缩放比例
 * @returns {string} 适配后的字体大小
 */
export function adaptFontSize(fontSize, scale = 1) {
    const adaptedSize = Math.round(fontSize * scale);
    return Math.max(12, Math.min(48, adaptedSize)) + 'px'; // 限制字体大小范围
}

/**
 * 批量转换样式对象
 * @param {Object} styles 样式对象
 * @param {number} scale 缩放比例
 * @returns {Object} 适配后的样式对象
 */
export function adaptStyles(styles, scale = 1) {
    const adaptedStyles = {};
    
    for (const [key, value] of Object.entries(styles)) {
        if (typeof value === 'string' && value.endsWith('px')) {
            const pxValue = parseFloat(value);
            if (key.includes('font') || key.includes('Font')) {
                adaptedStyles[key] = adaptFontSize(pxValue, scale);
            } else {
                adaptedStyles[key] = adaptPx(pxValue, scale);
            }
        } else {
            adaptedStyles[key] = value;
        }
    }
    
    return adaptedStyles;
}

/**
 * 创建响应式CSS变量
 * @param {number} scale 缩放比例
 * @param {boolean} is11Inch 是否为11英寸屏幕
 * @returns {string} CSS变量字符串
 */
export function createResponsiveCSSVars(scale = 1, is11Inch = false) {
    // 11英寸屏幕专用的尺寸调整
    const fontScale = is11Inch ? 0.9 : 1; // 字体额外缩小10%
    const spacingScale = is11Inch ? 0.8 : 1; // 间距额外缩小20%

    return `
        :root {
            --responsive-scale: ${scale};
            --font-size-xs: ${adaptFontSize(12 * fontScale, scale)};
            --font-size-sm: ${adaptFontSize(14 * fontScale, scale)};
            --font-size-base: ${adaptFontSize(16 * fontScale, scale)};
            --font-size-lg: ${adaptFontSize(18 * fontScale, scale)};
            --font-size-xl: ${adaptFontSize(20 * fontScale, scale)};
            --font-size-xxl: ${adaptFontSize(22 * fontScale, scale)};
            --font-size-title: ${adaptFontSize(30 * fontScale, scale)};

            --spacing-xs: ${adaptPx(4 * spacingScale, scale)};
            --spacing-sm: ${adaptPx(8 * spacingScale, scale)};
            --spacing-base: ${adaptPx(12 * spacingScale, scale)};
            --spacing-lg: ${adaptPx(16 * spacingScale, scale)};
            --spacing-xl: ${adaptPx(24 * spacingScale, scale)};
            --spacing-xxl: ${adaptPx(32 * spacingScale, scale)};

            --border-radius-sm: ${adaptPx(4, scale)};
            --border-radius-base: ${adaptPx(8, scale)};
            --border-radius-lg: ${adaptPx(12, scale)};

            --header-height: ${adaptPx(is11Inch ? 41 : 82, scale)}; // 11英寸屏幕头部高度减半
            --header-height-assessment-record: ${adaptPx(is11Inch ? 44 : 88, scale)}; // 测评记录页面头部高度减半
            --status-bar-height: ${adaptPx(44 * (is11Inch ? 0.9 : 1), scale)};
            --table-cell-height: ${adaptPx(61 * (is11Inch ? 0.85 : 1), scale)};
            --table-header-height: ${adaptPx(is11Inch ? 30 : 60, scale)}; // 11英寸屏幕表头高度减半
            --button-height: ${adaptPx(44 * (is11Inch ? 0.9 : 1), scale)};
            --filter-height: ${adaptPx(is11Inch ? 40 : 44, scale)};
        }
    `;
}

// 全局缓存缩放信息
let globalScale = null;

/**
 * 初始化响应式适配
 */
export async function initResponsive() {
    try {
        const deviceInfo = await getDeviceInfo();
        const scaleInfo = calculateScale(deviceInfo);
        globalScale = scaleInfo.finalScale;
        
        // 注入CSS变量
        const cssVars = createResponsiveCSSVars(globalScale, scaleInfo.is11InchScreen);

        // #ifdef H5
        if (typeof document !== 'undefined') {
            let styleEl = document.getElementById('responsive-vars');
            if (!styleEl) {
                styleEl = document.createElement('style');
                styleEl.id = 'responsive-vars';
                document.head.appendChild(styleEl);
            }
            styleEl.innerHTML = cssVars;

            // 为11英寸屏幕添加特殊CSS类
            if (scaleInfo.is11InchScreen) {
                document.documentElement.classList.add('screen-11inch');
                console.log('检测到11英寸屏幕，已应用专用优化样式');
            } else {
                // 移除11英寸屏幕类（如果之前添加过）
                document.documentElement.classList.remove('screen-11inch');
            }
        }
        // #endif
        
        console.log('响应式适配初始化完成:', {
            deviceInfo,
            scaleInfo,
            finalScale: globalScale,
            is11InchScreen: scaleInfo.is11InchScreen
        });
        
        return scaleInfo;
    } catch (error) {
        console.error('响应式适配初始化失败:', error);
        globalScale = 1;
        return { finalScale: 1, is11InchScreen: false };
    }
}

/**
 * 获取当前缩放比例
 */
export function getCurrentScale() {
    return globalScale || 1;
}

/**
 * 响应式混入对象（Vue组件使用）
 */
export const responsiveMixin = {
    data() {
        return {
            responsiveScale: 1
        };
    },
    async created() {
        const scaleInfo = await initResponsive();
        this.responsiveScale = scaleInfo.finalScale;
    },
    methods: {
        adaptPx(px) {
            return adaptPx(px, this.responsiveScale);
        },
        adaptFontSize(fontSize) {
            return adaptFontSize(fontSize, this.responsiveScale);
        },
        adaptStyles(styles) {
            return adaptStyles(styles, this.responsiveScale);
        }
    }
};
