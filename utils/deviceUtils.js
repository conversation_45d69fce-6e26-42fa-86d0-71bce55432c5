/**
 * 设备工具类
 * @description 用于获取设备唯一标识、IP地址等设备信息
 */

/**
 * 获取设备唯一标识
 * @returns {Promise<string>} 设备ID
 */
export function getDeviceId() {
    return new Promise((resolve) => {
        // #ifdef H5
        // H5环境下使用浏览器指纹作为设备ID
        const deviceId = generateBrowserFingerprint();
        resolve(deviceId);
        // #endif
        
        // #ifdef APP-PLUS
        // App环境下获取设备信息
        uni.getSystemInfo({
            success: (res) => {
                // 优先使用设备唯一标识
                let deviceId = res.deviceId || res.uuid || res.system;
                
                // 如果没有设备ID，则生成一个基于设备信息的唯一标识
                if (!deviceId) {
                    deviceId = generateDeviceFingerprint(res);
                }
                
                resolve(deviceId);
            },
            fail: () => {
                // 获取失败时生成一个随机ID
                const fallbackId = 'device_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                resolve(fallbackId);
            }
        });
        // #endif
        
        // #ifdef MP
        // 小程序环境
        uni.getSystemInfo({
            success: (res) => {
                // 小程序环境下的设备标识
                let deviceId = res.system + '_' + res.platform + '_' + res.brand + '_' + res.model;
                deviceId = deviceId.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_]/g, '');
                resolve(deviceId);
            },
            fail: () => {
                const fallbackId = 'mp_device_' + Date.now();
                resolve(fallbackId);
            }
        });
        // #endif
    });
}

/**
 * 生成浏览器指纹（H5环境）
 * @returns {string} 浏览器指纹
 */
function generateBrowserFingerprint() {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    ctx.textBaseline = 'top';
    ctx.font = '14px Arial';
    ctx.fillText('Device fingerprint', 2, 2);
    
    const fingerprint = [
        navigator.userAgent,
        navigator.language,
        screen.width + 'x' + screen.height,
        new Date().getTimezoneOffset(),
        navigator.platform,
        canvas.toDataURL()
    ].join('|');
    
    // 生成简单的hash
    let hash = 0;
    for (let i = 0; i < fingerprint.length; i++) {
        const char = fingerprint.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // 转换为32位整数
    }
    
    return 'browser_' + Math.abs(hash).toString(36);
}

/**
 * 生成设备指纹（App环境）
 * @param {Object} systemInfo - 系统信息
 * @returns {string} 设备指纹
 */
function generateDeviceFingerprint(systemInfo) {
    const {
        platform,
        system,
        brand,
        model,
        screenWidth,
        screenHeight,
        pixelRatio
    } = systemInfo;
    
    const fingerprint = [
        platform || 'unknown',
        system || 'unknown',
        brand || 'unknown',
        model || 'unknown',
        screenWidth || 0,
        screenHeight || 0,
        pixelRatio || 1
    ].join('_');
    
    // 清理特殊字符
    const cleanFingerprint = fingerprint.replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_]/g, '');
    
    return 'app_' + cleanFingerprint + '_' + Date.now().toString(36);
}

/**
 * 获取设备IP地址（仅在支持的环境下）
 * @returns {Promise<string>} IP地址
 */
export function getDeviceIP() {
    return new Promise((resolve) => {
        // #ifdef H5
        // H5环境下无法直接获取本地IP，返回默认值
        resolve('127.0.0.1');
        // #endif
        
        // #ifdef APP-PLUS
        // App环境下尝试获取网络信息
        uni.getNetworkType({
            success: (res) => {
                // 这里只能获取网络类型，无法获取具体IP
                // 实际项目中可能需要调用原生插件获取IP
                resolve('*************'); // 默认IP
            },
            fail: () => {
                resolve('*************'); // 默认IP
            }
        });
        // #endif
        
        // #ifdef MP
        // 小程序环境下无法获取IP
        resolve('127.0.0.1');
        // #endif
    });
}

/**
 * 获取完整的设备信息
 * @returns {Promise<Object>} 设备信息对象
 */
export function getDeviceInfo() {
    return new Promise(async (resolve) => {
        try {
            const deviceId = await getDeviceId();
            const deviceIP = await getDeviceIP();
            
            uni.getSystemInfo({
                success: (systemInfo) => {
                    resolve({
                        deviceId,
                        deviceIP,
                        platform: systemInfo.platform,
                        system: systemInfo.system,
                        brand: systemInfo.brand,
                        model: systemInfo.model,
                        screenWidth: systemInfo.screenWidth,
                        screenHeight: systemInfo.screenHeight,
                        pixelRatio: systemInfo.pixelRatio,
                        timestamp: Date.now()
                    });
                },
                fail: () => {
                    resolve({
                        deviceId,
                        deviceIP,
                        platform: 'unknown',
                        system: 'unknown',
                        brand: 'unknown',
                        model: 'unknown',
                        timestamp: Date.now()
                    });
                }
            });
        } catch (error) {
            console.error('获取设备信息失败:', error);
            resolve({
                deviceId: 'fallback_' + Date.now(),
                deviceIP: '127.0.0.1',
                platform: 'unknown',
                system: 'unknown',
                brand: 'unknown',
                model: 'unknown',
                timestamp: Date.now()
            });
        }
    });
}

/**
 * 缓存设备ID到本地存储
 * @param {string} deviceId - 设备ID
 */
export function cacheDeviceId(deviceId) {
    try {
        uni.setStorageSync('cached_device_id', deviceId);
    } catch (error) {
        console.error('缓存设备ID失败:', error);
    }
}

/**
 * 从本地存储获取缓存的设备ID
 * @returns {string|null} 缓存的设备ID
 */
export function getCachedDeviceId() {
    try {
        return uni.getStorageSync('cached_device_id');
    } catch (error) {
        console.error('获取缓存设备ID失败:', error);
        return null;
    }
}

/**
 * 获取或生成设备ID（优先使用缓存）
 * @returns {Promise<string>} 设备ID
 */
export async function getOrGenerateDeviceId() {
    // 先尝试从缓存获取
    const cachedId = getCachedDeviceId();
    if (cachedId) {
        return cachedId;
    }
    
    // 缓存中没有则重新生成
    const deviceId = await getDeviceId();
    cacheDeviceId(deviceId);
    return deviceId;
}
