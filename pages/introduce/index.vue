<template>
	<view class="introduce">
		<video :loop="false" v-if="videoMusic" style="position: fixed;top: -100vh;left: -200vw;" id="myVideo" src="/static/audio/intro.mp3" :autoplay="true" />
		<image src="/static/introduce.png" mode="widthFix" class="introduce-img"></image>
		<view class="introduce-radio center" @click="agagin"><text class="introduce-radio-icon iconfont">&#xe6b3;</text>朗读</view>
		<view class="introduce-btn">
			<view class="introduce-btn-left center" @click="navigateBack()">
				取消
			</view>
			<view class="introduce-btn-center center" @click="goTest">
				进入演示题练习
			</view>
			<view class="introduce-btn-right center" @click="go">
				跳过演示，正式测评
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		reactive,
		ref,
		watch,
		onUnmounted
	} from "vue";
	import {
		navigateTo,
		navigateBack,
		redirectTo
	} from '../../common/uniTool';
	import {
		onHide
	} from '@dcloudio/uni-app'
	
	const props = defineProps(['projectId', 'projectName', 'workOrderId', 'traineeId'])
	const videoMusic = ref(true)
	onMounted(() => {
		videoMusic.value = true
	})
	onHide(() => {
		videoMusic.value = false
	})
	const goTest = () => {
		const baseParams = `projectId=${props.projectId || ''}&projectName=${encodeURIComponent(props.projectName || '')}&workOrderId=${props.workOrderId || ''}&traineeId=${props.traineeId || ''}`
		redirectTo(`/pages/question/test?${baseParams}`)
	}
	const go = () => {
		const baseParams = `projectId=${props.projectId || ''}&projectName=${encodeURIComponent(props.projectName || '')}&workOrderId=${props.workOrderId || ''}&traineeId=${props.traineeId || ''}`
		redirectTo(`/pages/question/index?${baseParams}`)
	}
	onUnmounted(() => {
		videoMusic.value = false
	})
	const agagin = () => {
		videoMusic.value = false
		videoMusic.value = true
	}
</script>

<style lang="scss">
	.introduce {
		width: 100vw;
		height: 100vh;
		position: relative;

		&-img {
			width: 100%;
			position: absolute;
			top: 0;
			left: 0;
			z-index: -1;
		}

		&-radio {
			width: 343rpx;
			height: 103rpx;
			border-radius: 17rpx;
			border: 4rpx solid #111111;
			font-size: 69rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #111111;
			position: absolute;
			right: 120rpx;
			top: 120rpx;

			&-icon {
				margin-right: 16rpx;
			}
		}

		&-btn {
			display: flex;
			align-items: center;
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
			bottom: 200rpx;
			width: 90%;
			justify-content: space-around;

			&-left {
				width: 352rpx;
				height: 172rpx;
				border-radius: 86rpx;
				border: 4rpx solid #41A8DE;
				font-size: 86rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #41A8DE;
			}

			&-center {
				width: 720rpx;
				height: 172rpx;
				background: #41A8DE;
				border-radius: 86rpx;
				font-size: 78rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #FFFFFF;
			}

			&-right {
				width: 820rpx;
				height: 172rpx;
				border-radius: 86rpx;
				border: 4rpx solid #41A8DE;
				font-size: 78rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #41A8DE;
			}
		}
	}
</style>