<template>
	<view class="report">
		<view class="report-result" v-if="state.result">
			<view class="report-result-left">
				<view>SNAP-IV 父母评定问卷结果： <text style="font-weight: bold;color: #FF0000;font-size: 24rpx;">{{state.result.score}}</text>分</view>
				<view><text>可能存在<text style="font-weight: bold;color: #FF0000;">多动症{{state.result.level}}</text>问题；具体情况建议结合临床</text></view>
			</view>
			<view class="report-result-right">
				<view class="report-result-right-text" @click="downLoad">
					<text class="iconfont">&#xe648;</text> 报告下载
				</view>
				<view class="report-result-right-box" v-if="state.showPop">
					<view v-for="(item,index) in downTypes" :class="!index?'report-result-right-box-item':''" :key="index" @click="open(item.value)">
						{{item.name}}
					</view>
				</view>
			</view>
		</view>
		<view class="report-num" v-if="state.result">
			<view class="report-num-item center" @click="state.showModel=1" :class="state.showModel===1?'report-num-item-click':''">
				<text class="report-num-item-top">注意力IA</text>
				<view class="report-num-item-bottom" :class="state.showModel===1?'report-num-item-bottom-click':''">
					平均分：{{(state.result['signalData'].slice(0,9).map(item=>item.select).reduce((a,b)=>a+b)/state.result['signalData'].slice(0,9).length).toFixed(1)}}分
					<text>总分：{{state.result['signalData'].slice(0,9).map(item=>item.select).reduce((a,b)=>a+b)}}分</text>
				</view>
			</view>
			<view :class="state.showModel===2?'report-num-item-click':''" class="report-num-item center" @click="state.showModel=2">
				<text class="report-num-item-top">多动HI</text>
				<view class="report-num-item-bottom" :class="state.showModel===2?'report-num-item-bottom-click':''">
					平均分：{{(state.result['signalData'].slice(9,18).map(item=>item.select).reduce((a,b)=>a+b)/state.result['signalData'].slice(9,18).length).toFixed(1)}}分
					<text>总分：{{state.result['signalData'].slice(9,18).map(item=>item.select).reduce((a,b)=>a+b)}}分</text>
				</view>
			</view>
			<view :class="state.showModel===3?'report-num-item-click':''" class="report-num-item center" @click="state.showModel=3">
				<text class="report-num-item-top">对立违抗ODD</text>
				<view class="report-num-item-bottom" :class="state.showModel===3?'report-num-item-bottom-click':''">
					平均分：{{(state.result['signalData'].slice(18,27).map(item=>item.select).reduce((a,b)=>a+b)/state.result['signalData'].slice(18,27).length).toFixed(1)}}分
					<text>总分：{{state.result['signalData'].slice(18,27).map(item=>item.select).reduce((a,b)=>a+b)}}分</text>
				</view>
			</view>
		</view>
		<view class="report-title">
			<view class="report-title-left">
				题目
			</view>
			<view class="report-title-item center">
				没有
			</view>
			<view class="report-title-item center">
				有一点
			</view>
			<view class="report-title-item center">
				不少
			</view>
			<view class="report-title-item center">
				很多
			</view>
		</view>
		<view class="report-list" v-if="state.showModel===1">
			<view v-for="(item,index) in question.slice(0,9)" :class="index%2===0?'':'report-list-item-bg'" class="report-list-item" :key="index">
				<view class="report-list-item-left">{{index+1}}、{{item}}</view>
				<view class="report-list-item-answer" v-if="state.result">
					<view v-for="(item,index1) in answer" :key="index1" class="report-list-item-answer-item center"
						:class="state.result['signalData'][index]['select']===index1?'report-list-item-answer-item-click':''">
						<text>{{index1}}分</text>
					</view>
				</view>

			</view>
		</view>
		<view class="report-list" v-else-if="state.showModel===2">
			<view v-for="(item,index) in question.slice(9,18)" :class="index%2===0?'':'report-list-item-bg'" class="report-list-item" :key="index">
				<view class="report-list-item-left">{{index+10}}、{{item}}</view>
				<view class="report-list-item-answer" v-if="state.result">
					<view v-for="(item,index1) in answer" :key="index1" class="report-list-item-answer-item center"
						:class="state.result['signalData'][index]['select']===index1?'report-list-item-answer-item-click':''">
						<text>{{index1}}分</text>
					</view>
				</view>

			</view>
		</view>
		<view class="report-list" v-else-if="state.showModel===3">
			<view v-for="(item,index) in question.slice(18,27)" :class="index%2===0?'':'report-list-item-bg'" class="report-list-item" :key="index">
				<view class="report-list-item-left">{{index+19}}、{{item}}</view>
				<view class="report-list-item-answer" v-if="state.result">
					<view v-for="(item,index1) in answer" :key="index1" class="report-list-item-answer-item center"
						:class="state.result['signalData'][index]['select']===index1?'report-list-item-answer-item-click':''">
						<text>{{index1}}分</text>
					</view>
				</view>

			</view>
		</view>
	</view>
</template>
<script setup>
	import {
		onMounted,
		reactive,
		ref,
		onUnmounted
	} from "vue";
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		getStorageSync,
		showToast,
		setNavigationBarTitle,
		showLoading,
		hideLoading
	} from "../../common/uniTool";
	import {
		getQrySnapEvaDetail,
		exportSnapEvaDetail
	} from "../../service/scale";
	import {
		useUserStore
	} from '../../stores/user';
	const userStore = useUserStore()
	const props = defineProps(['round', 'userName', 'outpatientId'])
	const downTypes = [{
		name: 'PDF版本',
		value: 'pdf'
	}, {
		name: 'WORD版本',
		value: 'doc'
	}]
	onShow(() => {
		// #ifdef APP-PLUS
		setTimeout(() => {
			plus.navigator.setFullscreen(false);
			plus.screen.unlockOrientation(); //解除屏幕方向的锁定，但是不一定是竖屏；
			plus.screen.lockOrientation('portrait'); //锁死屏幕方向为竖屏
		}, 200)
		// #endif
	})
	onMounted(() => {
		getResult()
	})
	onUnmounted(() => {
		console.log('卸载');
		// #ifdef APP-PLUS
		plus.screen.unlockOrientation(); //解除屏幕方向的锁定，但是不一定是竖屏；
		plus.screen.lockOrientation('landscape-primary');
		// #endif
	})
	const downLoad = () => {
		exportSnapEvaDetail({
			round: props.round || null,
			outpatientId: props.outpatientId || userStore.outpatientId,
			userName: props.userName || userStore.userName,
		}).then(res => {
			state.showPop = true
			state.fileList = res.data
		})
	}
const open = (value) => {
    const ensureHttps = (url) => typeof url === 'string' && /^http:\/\//i.test(url) ? url.replace(/^http:\/\//i, 'https://') : url
    const raw = state.fileList[value]
    const url = ensureHttps(raw)
    const encodedUrl = encodeURI(url)
    const fileType = /\.pdf(\?|$)/i.test(url) ? 'pdf' : (/\.docx?(\?|$)/i.test(url) ? 'docx' : undefined)
    uni.downloadFile({
        url: encodedUrl,
        success: function (res) {
            if (res.statusCode !== 200) {
                showToast('报告下载失败')
                return
            }
            let filePath = res.tempFilePath
            console.log(filePath)
            showLoading('生成报告中....')
            const proceedOpen = (pathToOpen) => {
                uni.getFileInfo({
                    filePath: pathToOpen,
                    success: () => {
                        uni.openDocument({
                            filePath: pathToOpen,
                            fileType,
                            showMenu: true,
                            success: function () {
                                state.showPop = false
                                hideLoading()
                            },
                            fail() {
                                hideLoading()
                                if (typeof plus !== 'undefined' && plus.runtime && url) {
                                    try { plus.runtime.openURL(url) } catch (e) {}
                                }
                                showToast('打开失败')
                            }
                        })
                    },
                    fail: () => {
                        hideLoading()
                        showToast('文件校验失败')
                    }
                })
            }
            const afterSaved = (savedPath) => {
                if (typeof plus !== 'undefined') {
                    plus.io.resolveLocalFileSystemURL(savedPath, (entry) => {
                        entry.getParent((parent) => {
                            const ext = fileType || (/(\.\w+)$/.exec(savedPath)?.[1]?.replace('.', '') || 'pdf')
                            const newName = `report_${Date.now()}.${ext}`
                            entry.moveTo(parent, newName, (moved) => {
                                proceedOpen(moved.toLocalURL())
                            }, () => {
                                proceedOpen(savedPath)
                            })
                        }, () => proceedOpen(savedPath))
                    }, () => proceedOpen(savedPath))
                } else {
                    proceedOpen(savedPath)
                }
            }
            uni.saveFile({
                tempFilePath: filePath,
                success: ({ savedFilePath }) => afterSaved(savedFilePath),
                fail: () => afterSaved(filePath)
            })
        },
        fail: function () {
            showToast('报告下载失败')
        }
    })
}
	const getResult = () => {
		getQrySnapEvaDetail({
			round: props.round || null,
			outpatientId: props.outpatientId || userStore.outpatientId,
			userName: props.userName || userStore.userName,
		}).then(res => {
			setNavigationBarTitle(`SNAP-IV父母评定问卷-${res.data.userName}`)
			state.result = res.data
		}).catch(err => {
			showToast(err.msg)
		})
	}
	const question = ref([
		'专注细节部分困难，犯粗心大意错误（作业）',
		'很难维持注意力集中（听课，作业，谈话，或长时间阅读）',
		'与他/她讲话或口头指令时似听非听',
		'不能按要求完成作业及家务（开始任务后容易分心，很快失去注意力）',
		'很难有条理的安排任务和活动（任务无序、做事凌乱，没时间概念）',
		'不愿或回避需要持续动脑的任务（课堂作业或家庭作业）',
		'丢失学习和活动的必需品（学习资料、文具、红领巾、衣物）',
		'因干扰而分心',
		'日常生活中健忘、多动、冲动',
		'坐立不安、手脚不停',
		'在需要坐着的场合离开座位（教室里和其他公共场所）',
		'在不适宜的场所里跑来跑去、爬上爬下',
		'很难安静的参加同伴游戏或课余活动',
		'一刻不停，显得“精力旺盛”',
		'话很多',
		'问题没问完就抢着回答',
		'很难耐心排队或等待',
		'打断或干扰别人（如插话）对立违抗',
		'与大人争执',
		'发脾气',
		'不服从或拒绝遵从家长的要求或规定',
		'故意打扰别人',
		'把自己的错误和行为归咎于别人',
		'容易发怒或被激怒',
		'愤怒或怀恨在心',
		'充满恨意，想报复'
	])
	const answer = ref(['没有', '有一点', '不少', '很多'])
	const state = reactive({
		result: null,
		showModel: 1,
		showPop: false,
		fileList: null
	})
</script>
<style lang="scss">
	.report {
		width: 100vw;
		flex: 1;
		background: #F6F6F6;

		&-title {
			display: flex;
			padding: 0 16rpx;
			font-size: 20rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #FFFFFF;

			&-left {
				width: 58%;
				height: 52rpx;
				background: #41A8DE;
				padding-left: 16rpx;
				display: flex;
				align-items: center;

				&-value {
					padding: 20rpx 16rpx;
					height: 100%;
					background: #FFFFFF;
					font-size: 20rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
					line-height: 28rpx;
				}
			}

			&-item {
				background: #41A8DE;
				flex: 1;
				margin-left: 4rpx;

				&-value {
					background: #FFFFFF;
					font-size: 24rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
					line-height: 24rpx;

					&-click {
						background: #41A8DE;
						color: #FFFFFF;
					}
				}
			}
		}

		&-num {
			display: flex;
			font-size: 20rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #111111;
			margin-bottom: 15rpx;
			padding-left: 16rpx;

			&-item {
				width: 239rpx;
				height: 76rpx;
				background: #FFFFFF;
				border-radius: 8rpx;
				margin-right: 16rpx;
				display: flex;
				flex-direction: column;


				&-bottom {
					font-size: 16rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
					margin-top: 8rpx;

					&-click {
						font-size: 16rpx;
						font-family: PingFangSC-Semibold, PingFang SC;
						font-weight: 600;
						color: #008DD7;
					}
				}

				&-click {
					font-size: 20rpx;
					font-family: PingFangSC-Semibold, PingFang SC;
					font-weight: 600;
					color: #008DD7;
				}

			}
		}

		&-list {
			display: flex;
			flex-direction: column;
			width: 100%;
			padding: 0 16rpx;
			background: #FFFFFF;

			&-item {
				display: flex;
				width: 100%;
				margin: 2rpx 0;

				&-left {
					font-size: 20rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
					line-height: 30rpx;
					width: 58%;
					padding: 20rpx 16rpx;
				}

				&-answer {
					display: flex;
					flex: 1;

					&-item {
						flex: 1;
						margin-left: 4rpx;

						&-click {
							background: #41A8DE;
							color: #FFFFFF;
						}
					}
				}

				&-bg {
					background: #EDF9FF;
				}
			}
		}

		&-result {
			width: 100%;
			font-size: 22rpx;
			font-family: PingFangSC-Regular, PingFang SC;
			font-weight: 400;
			color: #111111;
			background: #FFFFFF;
			display: flex;
			align-items: flex-start;
			justify-content: space-between;
			padding: 14rpx 24rpx;
			margin-bottom: 15rpx;
			line-height: 38rpx;

			&-left {
				display: flex;
				flex-direction: column;
				align-items: flex-start;
			}

			&-right {
				color: #0991D7;
				border-radius: 8px;
				border: 1px solid #0991D7;
				position: relative;

				&-text {
					padding: 0 8rpx;
					font-size: 20px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
				}

				&-box {
					padding: 0 8rpx;
					position: absolute;
					top: 46rpx;
					left: 0;
					background: #FFFFFF;
					box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.25);
					border-radius: 8px;
					display: flex;
					align-items: center;
					flex-direction: column;
					font-size: 20px;
					font-family: PingFangSC, PingFang SC;
					font-weight: 400;
					color: #111111;

					&-item {
						border-bottom: 1px solid #E3E3E3;
					}
				}
			}
		}

	}
</style>