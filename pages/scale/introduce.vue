<template>
	<view class="page flex-col">
		<view class="text-wrapper_1 flex-row">
			<text class="text_3">
				为了更全面地了解您孩子专注与多动的情况，您需要填写SNAP-IV问卷的信息。
			</text>
		</view>
		<view class="text-wrapper_2 flex-row">
			<text class="paragraph_1">
				SNAP-IV量表由<PERSON>、<PERSON>等根据DSM(《美国精神障碍诊断与统计手册》)
				<br />
				中关于儿童注意缺陷多动障碍(ADHD)的诊断标准编制而成，因其项目与DSM-IV诊断项目直
				<br />
				接对应，因此临床使用的针对性较强，常被各大医院所使用。
			</text>
		</view>
		<view class="block_3 flex-row">
			<view class="text-group_1 flex-col " v-for="(item, index) in loopData0" :key="index">
				<text class="text_4">{{item.lanhutext0}}</text>
				<text class="text_5">{{item.lanhutext1}}</text>
			</view>
		</view>
		<view class="block_4 " @click="go">
			<view class="text-wrapper_3 flex-col center">
				<text class="text_6">开始填写</text>
			</view>
		</view>
	</view>
</template>
<script>
	import {
		redirectTo
	} from '../../common/uniTool';
	export default {
		props: ['projectId', 'projectName', 'workOrderId', 'traineeId'],
		data() {
			return {
				loopData0: [{
						lanhutext0: '3',
						lanhutext1: '行为维度(个)'
					},
					{
						lanhutext0: '26',
						lanhutext1: '题目数量(题)'
					},
					{
						lanhutext0: '5',
						lanhutext1: '测评耗时(分钟)'
					},
				],
				constants: {},
			};
		},
		methods: {
			go() {
				const baseParams = `projectId=${this.projectId || ''}&projectName=${encodeURIComponent(this.projectName || '')}&workOrderId=${this.workOrderId || ''}&traineeId=${this.traineeId || ''}`
				redirectTo(`/pages/scale/index?${baseParams}`)
			}
		},
	};
</script>
<style lang="scss">
	.page {
		background-color: rgba(246, 246, 246, 1);
		position: relative;
		width: 100vw;
		height: 100vh;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		align-items: center;

		.text-wrapper_1 {
			height: 28px;
			margin-top: 100rpx;

			.text_3 {
				width: 986px;
				height: 28px;
				overflow-wrap: break-word;
				color: rgba(17, 17, 17, 1);
				font-size: 55rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: bold;
				color: #111111;
				white-space: nowrap;
				line-height: 28px;
			}
		}

		.text-wrapper_2 {
			width: 972px;
			height: 120px;
			margin-top: 40rpx;

			.paragraph_1 {
				width: 972px;
				height: 120px;
				overflow-wrap: break-word;
				color: rgba(102, 102, 102, 1);
				font-size: 24px;
				font-weight: NaN;
				text-align: left;
				line-height: 40px;
			}
		}

		.block_3 {
			width: 745px;
			height: 116px;
			display: flex;
			margin-top: 70rpx;

			.text-group_1 {
				background-color: rgba(233, 233, 233, 1);
				border-radius: 8px;
				width: 227px;
				height: 116px;
				margin-right: 32px;
				display: flex;
				justify-content: center;
				align-items: center;
				flex-direction: column;

				.text_4 {
					color: rgba(17, 17, 17, 1);
					font-size: 40px;
					font-family: PingFangSC-Medium;
					font-weight: bold;
					text-align: left;
					line-height: 40px;
				}

				.text_5 {
					overflow-wrap: break-word;
					color: rgba(102, 102, 102, 1);
					font-size: 20px;
					line-height: 20px;
					margin-top: 36rpx;
				}
			}
		}

		.block_4 {
			width: 560px;
			height: 88px;
			margin-top: 180rpx;

			.text-wrapper_3 {
				background-color: rgba(65, 168, 222, 1);
				border-radius: 44px;
				height: 88px;
				width: 560px;

				.text_6 {
					width: 160px;
					height: 40px;
					color: rgba(255, 255, 255, 1);
					font-size: 40px;
					text-align: left;
					line-height: 40px;
				}
			}
		}
	}
</style>