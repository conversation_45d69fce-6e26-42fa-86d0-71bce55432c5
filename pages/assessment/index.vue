<template>
    <view class="assessment-page responsive-container" @click="closeAllDropdowns">
		<!-- 顶部功能区 -->
		<view class="header">
			<view class="header-bg"></view>
			<view class="back-btn" @click="goBack">
				<image src="/static/nav-back-icon.png" class="back-icon"></image>
			</view>
			<text class="title">测评工单</text>
			<view class="button-group">
				<view class="btn-new-order"
					:class="{ 'btn-loading': isNavigating }"
					@click="goToNewOrder"
					@tap="goToNewOrder">
					<view class="btn-icon" v-if="!isNavigating">
						<view class="icon-plus"></view>
					</view>
					<view class="loading-icon" v-if="isNavigating">...</view>
					<text class="btn-text">{{ isNavigating ? '跳转中...' : '新增工单' }}</text>
				</view>
			</view>
		</view>
		
        <!-- 表头筛选区 -->
        <view class="filter-header">
            <view class="search-frame">
                				<input type="text" v-model="searchText" placeholder="请输入患者名字/病案号查找" class="search-input responsive-input" />
				<view class="search-addon">
					<view class="search-icon"></view>
				</view>
			</view>
            <!-- 测评来源 下拉 -->
            <view class="filter-select" @click.stop="toggleSourceDropdown">
                <text class="filter-text" :class="{ active: filters.evaluationSourceType !== null }">{{ sourceDisplayText }}</text>
                <view class="arrow-down"></view>
                <view class="dropdown" v-if="showSourceDropdown" @click.stop>
                    <view class="dropdown-item" :class="{ selected: filters.evaluationSourceType === null }" @click="selectSource(null)">不选择</view>
                    <view class="dropdown-item" :class="{ selected: filters.evaluationSourceType === 0 }" @click="selectSource(0)">测评工单</view>
                    <view class="dropdown-item" :class="{ selected: filters.evaluationSourceType === 1 }" @click="selectSource(1)">单独测评</view>
                </view>
            </view>
            <!-- 状态 下拉 -->
            <view class="filter-select" @click.stop="toggleStateDropdown">
                <text class="filter-text" :class="{ active: !!filters.state }">{{ stateDisplayText }}</text>
                <view class="arrow-down"></view>
                <view class="dropdown" v-if="showStateDropdown" @click.stop>
                    <view class="dropdown-item" :class="{ selected: !filters.state }" @click="selectState('')">不选择</view>
                    <view class="dropdown-item" :class="{ selected: filters.state === 'TODO' }" @click="selectState('TODO')">待执行</view>
                    <view class="dropdown-item" :class="{ selected: filters.state === 'DOING' }" @click="selectState('DOING')">执行中</view>
                    <view class="dropdown-item" :class="{ selected: filters.state === 'DONE' }" @click="selectState('DONE')">已完成</view>
                    <view class="dropdown-item" :class="{ selected: filters.state === 'CANCELED' }" @click="selectState('CANCELED')">已撤销</view>
                </view>
            </view>
            <!-- 派单时间：点击开始/结束时间打开日历浮窗 -->
            <view id="begin-date-trigger" class="date-picker" @click.stop="openDatePopover('begin')">
                <text class="date-text" :class="{ active: !!filters.beginDate }">{{ filters.beginDate || '开始时间' }}</text>
                <view class="calendar-icon-wrapper">
                    <image src="/static/calendar-icon.png" class="calendar-icon"></image>
                </view>
            </view>
            <view id="end-date-trigger" class="date-picker" @click.stop="openDatePopover('end')">
                <text class="date-text" :class="{ active: !!filters.endDate }">{{ filters.endDate || '结束时间' }}</text>
                <view class="calendar-icon-wrapper">
                    <image src="/static/calendar-icon.png" class="calendar-icon"></image>
                </view>
            </view>
			<view class="button-group-right">
                <view class="btn-search responsive-button" @click.stop="onSearch">搜索</view>
                <view class="btn-reset responsive-button" @click.stop="onReset">重置</view>
			</view>
		</view>
		
		<!-- 表格和分页 -->
		<view class="table-container responsive-table">
			<view class="table-wrapper">
				<!-- 表格头部 -->
				<view class="table-header">
					<view class="table-cell header-cell col-index">序号</view>
					<view class="table-cell header-cell col-patient">患者信息</view>
					<view class="table-cell header-cell col-order">测评工单</view>
					<view class="table-cell header-cell col-time">派单时间</view>
                    <view class="table-cell header-cell col-status">状态</view>
                    <view class="table-cell header-cell col-action">操作</view>
				</view>
				
				<!-- 表格内容 -->
				<view class="table-body">
                    <view class="table-row" v-for="(item, index) in tableData" :key="item.workOrderId || index">
						<view class="table-cell col-index">{{ index + 1 }}</view>
						<view class="table-cell col-patient">
							<view class="patient-info">
                                <view class="patient-name">{{ item.traineeSimDesc }}</view>
							</view>
						</view>
                        <view class="table-cell col-order">{{ item.workOrderSimDesc }}</view>
                        <view class="table-cell col-time">{{ item.createTimeText }}</view>
                        <view class="table-cell col-status">
                            <view class="status-wrapper">
                                <view class="status-tag responsive-status-tag" :class="item.state">{{ item.stateText }}</view>
                                <view class="progress-tag responsive-status-tag" v-if="item.progress">{{ item.progress }}</view>
                            </view>
                        </view>
                        <view class="table-cell col-action">
                            <view class="action-btns">
                                <!-- 待执行：详情、撤销 -->
                                <view class="action-btn" v-if="item.state === 'TODO'">
                                    <text class="btn-text" @click="goToDetail(item)">详情</text>
                                    <view class="btn-divider"></view>
                                    <text class="btn-text" @click="cancelOrder(item)">撤销</text>
                                </view>
                                <!-- 执行中：详情、完成、撤销 -->
                                <view class="action-btn" v-else-if="item.state === 'DOING'">
                                    <text class="btn-text" @click="goToDetail(item)">详情</text>
                                    <view class="btn-divider"></view>
                                    <text class="btn-text" @click="completeOrder(item)">完成</text>
                                    <view class="btn-divider"></view>
                                    <text class="btn-text" @click="cancelOrder(item)">撤销</text>
                                </view>
                                <!-- 已完成：详情 -->
                                <view class="action-btn" v-else-if="item.state === 'DONE'">
                                    <text class="btn-text" @click="goToDetail(item)">详情</text>
                                </view>
                                <!-- 已撤销：详情、重新提交 -->
                                <view class="action-btn" v-else-if="item.state === 'CANCELED'">
                                    <text class="btn-text" @click="goToDetail(item)">详情</text>
                                    <view class="btn-divider"></view>
                                    <text class="btn-text" @click="resubmitOrder(item)">重新提交</text>
                                </view>
                                <!-- 其他：仅详情 -->
                                <view class="action-btn" v-else>
                                    <text class="btn-text" @click="goToDetail(item)">详情</text>
                                </view>
                            </view>
                        </view>
					</view>
				</view>
			</view>
			
			<!-- 分页 -->
			<view class="pagination responsive-pagination">
                <view class="pagination-left">
                    <text class="total-text">共{{ totalCount }}条</text>
                </view>
				<view class="pagination-controls">
                    <view class="page-size-selector">
                        <text class="page-size-text">{{ pageSize }}条/页</text>
						<view class="page-size-arrow"></view>
					</view>
					<view class="page-btn" :class="{ disabled: pageIndex <= 1 }" @click="prevPage">
						<view class="arrow-left"></view>
					</view>
					<view class="page-numbers">
						<!-- 动态页码显示 -->
						<template v-for="page in visiblePages" :key="page">
							<view 
								v-if="page === '...'" 
								class="page-ellipsis">···</view>
							<view 
								v-else
								class="page-number" 
								:class="{ active: page === pageIndex }" 
								@click="goToPage(page)">{{ page }}</view>
						</template>
					</view>
					<view class="page-btn" :class="{ disabled: pageIndex >= totalPages }" @click="nextPage">
						<view class="arrow-right"></view>
					</view>
				</view>
				<view class="pagination-right">
					<text class="goto-text">前往</text>
					<view class="goto-input">
						<input type="number" v-model="gotoPageInput" @confirm="gotoPage" class="goto-input-field" />
					</view>
					<text class="goto-text">页</text>
				</view>
			</view>
		</view>
        <!-- 浮窗日历（相对定位在筛选区域内） -->
        <view v-if="showDatePopover" class="date-popover responsive-date-popover"
              :style="{ top: datePopover.top + 'px', left: datePopover.left + 'px' }"
              @click.stop>
            <view class="popover-header">
                <view class="nav-btn" @click="prevMonth">‹</view>
                <view class="ym-text">{{ popoverYear }}年{{ popoverMonth }}月</view>
                <view class="nav-btn" @click="nextMonth">›</view>
            </view>
            <view class="week-row">
                <text v-for="w in ['日','一','二','三','四','五','六']" :key="w" class="week-cell">{{ w }}</text>
            </view>
            <view class="day-grid">
                <view v-for="(cell, idx) in calendarCells" :key="idx"
                      class="day-cell"
                      :class="{ other: cell.isOtherMonth, selected: cell.fullDate === selectedTempDate }"
                      @click="onSelectDate(cell)">
                    {{ cell.day }}
                </view>
            </view>
        </view>
        
        <!-- 日历（区间）选择器（保留但默认隐藏，H5 使用上方浮窗） -->
        <uv-calendar
            ref="calendarRef"
            :show="false"
            mode="range"
            :default-date="calendarDefaultRange"
            title="选择派单时间"
            start-text="开始"
            end-text="结束"
            :close-on-click-overlay="true"
            @confirm="onCalendarConfirm"
            @close="onCalendarClose"
        />
        
        <!-- 设备冲突确认弹窗 -->
        <DeviceConflictModal
            ref="deviceConflictModal"
            :visible="showDeviceConflictModal"
            @cancel="handleDeviceConflictCancel"
            @confirm="handleDeviceConflictConfirm"
            @maskClick="handleDeviceConflictCancel"
        />
    </view>
</template>

<script>
    import { getTime } from '../../common/method.js'
    import { qryWorkOrderListPage, updateWorkOrderState, reSubmitWorkOrder, verifyPassword } from '@/service/workorder.js'
    import { initResponsive, getCurrentScale, responsiveMixin } from '@/utils/responsive.js'
    import WorkOrderWebSocketClass, { MESSAGE_TYPES } from '@/utils/workOrderWebSocket.js'
    import { getOrGenerateDeviceId } from '@/utils/deviceUtils.js'
    import { useUserStore } from '@/stores/user.js'
    import { wsUrl } from '@/common/global.js'
    import DeviceConflictModal from '@/components/DeviceConflictModal.vue'
	export default {
        mixins: [responsiveMixin],
        components: {
            DeviceConflictModal
        },
		data() {
			return {
				pageIndex: 1,
				pageSize: 10,
				totalPages: 1,
				totalCount: 0,
				isNavigating: false, // 防止重复跳转标志
                // 顶部筛选
                filters: {
                    evaluationSourceType: null, // 0: 测评工单, 1: 单独测评
                    state: '', // TODO/DOING/DONE/CANCELED
                    beginDate: '',
                    endDate: ''
                },
                searchText: '',
                showSourceDropdown: false,
                showStateDropdown: false,
				showCalendar: false,
                    showDatePopover: false,
                    datePopover: { top: 0, left: 0 },
                    popoverAnchor: '', // 'begin' | 'end'
                    popoverDate: new Date(),
                    selectedTempDate: '',
				tableData: [],
				gotoPageInput: 1,
                // WebSocket相关
                workOrderWS: null,
                wsConnected: false,
                wsConnecting: false,
                deviceId: '',
                userStore: null,
                // 设备冲突相关
                showDeviceConflictModal: false,
                currentWorkOrderId: '',
                currentWorkOrderItem: null,
                // 加载状态
                isOpeningOrder: false,
                openOrderTimeout: null
			}
		},
		async onShow() {
			// 页面显示时重置跳转状态
			this.isNavigating = false
			
			//#ifdef APP-PLUS
			// 确保页面为全屏模式
			plus.navigator.setFullscreen(true);
			//#endif
            
            // 初始化响应式适配
            try {
                await initResponsive();
                console.log('页面响应式适配初始化完成, 当前缩放比例:', getCurrentScale());
            } catch (error) {
                console.error('响应式适配初始化失败:', error);
            }
            
            // 初始化设备ID（WebSocket连接将在需要时建立）
            await this.initDeviceId();

			this.getList()
		},
        
        onHide() {
            // 页面隐藏时不关闭WebSocket，保持连接状态
        },

        onUnload() {
            // 页面卸载时清理WebSocket连接
            this.cleanupWebSocket();
        },
        
        computed: {
            popoverYear() { return this.popoverDate.getFullYear() },
            popoverMonth() { return this.popoverDate.getMonth() + 1 },
            calendarCells() { return this.getMonthCells(this.popoverDate) },
            sourceDisplayText() {
                if (this.filters.evaluationSourceType === 0) return '测评工单'
                if (this.filters.evaluationSourceType === 1) return '单独测评'
                return '测评来源'
            },
            stateDisplayText() {
                const map = { TODO: '待执行', DOING: '执行中', DONE: '已完成', CANCELED: '已撤销' }
                return map[this.filters.state] || '状态'
            },
            dateRangeDisplayText() {
                const { beginDate, endDate } = this.filters
                if (beginDate || endDate) {
                    const begin = beginDate || '开始时间'
                    const end = endDate || '今天'
                    return `${begin} 至 ${end}`
                }
                return '派单时间'
            },
            calendarDefaultRange() {
                const today = getTime(Date.now())
                const start = this.filters.beginDate || ''
                const end = this.filters.endDate || today
                return start ? [start, end] : ['', end]
            },
            // 计算显示的页码
            visiblePages() {
                const current = this.pageIndex
                const total = this.totalPages
                const pages = []
                
                if (total <= 7) {
                    // 总页数少于等于7页，全部显示
                    for (let i = 1; i <= total; i++) {
                        pages.push(i)
                    }
                } else {
                    // 总页数大于7页，智能显示
                    if (current <= 4) {
                        // 当前页在前4页
                        for (let i = 1; i <= 5; i++) {
                            pages.push(i)
                        }
                        pages.push('...')
                        pages.push(total)
                    } else if (current >= total - 3) {
                        // 当前页在后4页
                        pages.push(1)
                        pages.push('...')
                        for (let i = total - 4; i <= total; i++) {
                            pages.push(i)
                        }
                    } else {
                        // 当前页在中间
                        pages.push(1)
                        pages.push('...')
                        for (let i = current - 1; i <= current + 1; i++) {
                            pages.push(i)
                        }
                        pages.push('...')
                        pages.push(total)
                    }
                }
                
                return pages
            }
        },
		methods: {
            // 获取工单列表
            async getList() {
                const query = {
                    pageSize: this.pageSize,
                    pageIndex: this.pageIndex
                }
                const keyword = (this.searchText || '').trim()
                query.param = { param: keyword }
                if (this.filters.evaluationSourceType === 0 || this.filters.evaluationSourceType === 1) {
                    query.evaluationSourceType = this.filters.evaluationSourceType
                }
                if (this.filters.state) {
                    query.state = this.filters.state
                }
                if (this.filters.beginDate) {
                    query.beginDate = this.convertDateToTimestamp(this.filters.beginDate, false)
                }
                if (this.filters.endDate) {
                    query.endDate = this.convertDateToTimestamp(this.filters.endDate, true)
                }

                const res = await qryWorkOrderListPage(query)
                const stateTextMap = { TODO: '待执行', DOING: '执行中', DONE: '已完成', CANCELED: '已撤销' }
                this.tableData = (res.data || []).map(item => ({
                    ...item,
                    stateText: stateTextMap[item.state] || item.state,
                    createTimeText: this.formatUtcToLocal(item.createTime)
                }))
                this.totalCount = res.total || 0
                this.totalPages = Math.max(1, Math.ceil(this.totalCount / this.pageSize))
            },
            formatUtcToLocal(utcStr) {
                if (!utcStr) return ''
                const d = new Date(utcStr)
                if (isNaN(d.getTime())) return utcStr
                const Y = d.getFullYear()
                const M = (d.getMonth() + 1).toString().padStart(2, '0')
                const D = d.getDate().toString().padStart(2, '0')
                const h = d.getHours().toString().padStart(2, '0')
                const m = d.getMinutes().toString().padStart(2, '0')
                const s = d.getSeconds().toString().padStart(2, '0')
                return `${Y}-${M}-${D} ${h}:${m}:${s}`
            },
			goBack() {
				uni.navigateBack()
			},
			goToNewOrder() {
				// 防止重复点击
				if (this.isNavigating) {
					console.log('正在跳转中，忽略重复点击')
					return
				}
				
				console.log('点击新增工单按钮')
				this.isNavigating = true
				
				uni.navigateTo({
					url: '/pages/assessment/add',
					success: (res) => {
						console.log('跳转成功', res)
					},
					fail: (err) => {
						console.log('跳转失败', err)
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						})
					},
					complete: () => {
						// 无论成功还是失败，都重置状态
						setTimeout(() => {
							this.isNavigating = false
						}, 1000)
					}
				})
			},
            // 下拉：测评来源
            toggleSourceDropdown() {
                this.showSourceDropdown = !this.showSourceDropdown
                if (this.showSourceDropdown) this.showStateDropdown = false
            },
            selectSource(type) {
                // 再次点击同一选项可取消选择
                if (this.filters.evaluationSourceType === type) {
                    this.filters.evaluationSourceType = null
                } else {
                    this.filters.evaluationSourceType = type
                }
                this.showSourceDropdown = false
            },
            // 下拉：状态
            toggleStateDropdown() {
                this.showStateDropdown = !this.showStateDropdown
                if (this.showStateDropdown) this.showSourceDropdown = false
            },
            selectState(code) {
                // 再次点击同一选项可取消选择
                if (this.filters.state === code) {
                    this.filters.state = ''
                } else {
                    this.filters.state = code
                }
                this.showStateDropdown = false
            },
            // 关闭所有下拉
            closeAllDropdowns() {
                this.showSourceDropdown = false
                this.showStateDropdown = false
                this.showDatePopover = false
            },
            // 点击触发：在按钮下方打开浮窗日历
            openDatePopover(anchor) {
                console.log('打开日历弹窗:', anchor)
                this.popoverAnchor = anchor
                // 初始化临时选中日期：优先取当前已有值
                const init = anchor === 'begin' ? (this.filters.beginDate || this.filters.endDate) : (this.filters.endDate || this.filters.beginDate)
                this.selectedTempDate = init || this.formatDate(new Date())
                this.popoverDate = init ? new Date(init) : new Date()

                // 计算弹窗位置
                this.$nextTick(() => {
                    this.calculatePopoverPosition(anchor)
                    this.showDatePopover = true
                })
                console.log('弹窗状态:', { showDatePopover: this.showDatePopover, popoverAnchor: this.popoverAnchor })
            },

            // 计算弹窗位置
            calculatePopoverPosition(anchor) {
                const triggerId = anchor === 'begin' ? 'begin-date-trigger' : 'end-date-trigger'

                // 尝试获取触发元素的位置信息
                uni.createSelectorQuery().in(this).select(`#${triggerId}`).boundingClientRect((rect) => {
                    if (rect) {
                        console.log('触发元素位置信息:', rect)
                        // 设置弹窗位置：在按钮下方，左对齐
                        this.datePopover = {
                            top: rect.bottom + 8, // 按钮下方8px
                            left: rect.left // 与按钮左对齐
                        }
                        console.log('计算的弹窗位置:', this.datePopover)
                    } else {
                        console.warn('无法获取触发元素位置，使用默认位置')
                        // 使用默认位置
                        this.datePopover = {
                            top: 200,
                            left: anchor === 'begin' ? 400 : 650
                        }
                    }
                }).exec()
            },

            // 工具：格式化 yyyy-MM-dd
            formatDate(d) {
                const Y = d.getFullYear()
                const M = (d.getMonth() + 1).toString().padStart(2, '0')
                const D = d.getDate().toString().padStart(2, '0')
                return `${Y}-${M}-${D}`
            },
            // 工具：将 yyyy-MM-dd 转为 13 位时间戳（开始: 00:00:00.000，结束: 23:59:59.999）
            convertDateToTimestamp(dateStr, endOfDay = false) {
                if (!dateStr || typeof dateStr !== 'string') return undefined
                const parts = dateStr.split('-')
                if (parts.length !== 3) return undefined
                const year = parseInt(parts[0], 10)
                const month = parseInt(parts[1], 10) - 1
                const day = parseInt(parts[2], 10)
                if (Number.isNaN(year) || Number.isNaN(month) || Number.isNaN(day)) return undefined
                const date = endOfDay
                    ? new Date(year, month, day, 23, 59, 59, 999)
                    : new Date(year, month, day, 0, 0, 0, 0)
                return date.getTime()
            },
            // 生成当前弹窗月份的日历单元（含前后月补位）
            getMonthCells(date) {
                const year = date.getFullYear()
                const month = date.getMonth() // 0-11
                const firstDay = new Date(year, month, 1)
                const startWeek = firstDay.getDay() // 0-6, 周日为0
                const daysInMonth = new Date(year, month + 1, 0).getDate()
                const prevDays = startWeek
                const cells = []
                // 上月补位
                if (prevDays > 0) {
                    const prevLastDate = new Date(year, month, 0).getDate()
                    for (let i = prevDays - 1; i >= 0; i--) {
                        const day = prevLastDate - i
                        const full = this.formatDate(new Date(year, month - 1, day))
                        cells.push({ day, fullDate: full, isOtherMonth: true })
                    }
                }
                // 当月
                for (let d = 1; d <= daysInMonth; d++) {
                    const full = this.formatDate(new Date(year, month, d))
                    cells.push({ day: d, fullDate: full, isOtherMonth: false })
                }
                // 下月补足至 6 行 x 7 列 = 42
                while (cells.length % 7 !== 0 || cells.length < 42) {
                    const nextIndex = cells.length - (prevDays + daysInMonth) + 1
                    const full = this.formatDate(new Date(year, month + 1, nextIndex))
                    cells.push({ day: nextIndex, fullDate: full, isOtherMonth: true })
                }
                return cells
            },
            // 选择某天
            onSelectDate(cell) {
                this.selectedTempDate = cell.fullDate
                if (this.popoverAnchor === 'begin') {
                    this.filters.beginDate = cell.fullDate
                    // 若开始大于结束，自动同步结束
                    if (this.filters.endDate && this.filters.beginDate > this.filters.endDate) {
                        this.filters.endDate = this.filters.beginDate
                    }
                } else {
                    this.filters.endDate = cell.fullDate
                    // 若结束小于开始，自动同步开始
                    if (this.filters.beginDate && this.filters.endDate < this.filters.beginDate) {
                        this.filters.beginDate = this.filters.endDate
                    }
                }
                this.showDatePopover = false
            },
            // 月份切换
            prevMonth() {
                const y = this.popoverDate.getFullYear()
                const m = this.popoverDate.getMonth()
                this.popoverDate = new Date(y, m - 1, 1)
            },
            nextMonth() {
                const y = this.popoverDate.getFullYear()
                const m = this.popoverDate.getMonth()
                this.popoverDate = new Date(y, m + 1, 1)
            },
            onCalendarConfirm(e) {
                console.log('日历确认回调:', e)
                // uv-calendar 在 range 模式下，e 是一个数组 [startDate, endDate]
                if (Array.isArray(e) && e.length >= 2) {
                    this.filters.beginDate = e[0] || ''
                    this.filters.endDate = e[1] || ''
                    this.showCalendar = false
                    return
                }
                // 兼容其他格式
                if (Array.isArray(e) && e.length === 1) {
                    this.filters.beginDate = e[0] || ''
                    this.filters.endDate = e[0] || ''
                    this.showCalendar = false
                    return
                }
                // 对象格式兼容
                if (e && typeof e === 'object') {
                    if (e.startDate || e.endDate) {
                        this.filters.beginDate = e.startDate || ''
                        this.filters.endDate = e.endDate || ''
                        this.showCalendar = false
                        return
                    }
                    if (e.fulldate) {
                        // 单选降级为同一天
                        this.filters.beginDate = e.fulldate
                        this.filters.endDate = e.fulldate
                        this.showCalendar = false
                        return
                    }
                }
                this.showCalendar = false
            },
            onCalendarClose() {
                console.log('日历关闭')
                this.showCalendar = false
            },
            async onSearch() {
                this.pageIndex = 1
                await this.getList()
            },
            onReset() {
                this.filters = { evaluationSourceType: null, state: '', beginDate: '', endDate: '' }
                this.searchText = ''
                this.showCalendar = false
                this.pageIndex = 1
                this.tableData = []
                this.totalCount = 0
                this.totalPages = 1
                this.getList()
            },
            prevPage() {
                if (this.pageIndex > 1) {
                    this.pageIndex--
                    this.getList()
                }
            },
            nextPage() {
                if (this.pageIndex < this.totalPages) {
                    this.pageIndex++
                    this.getList()
                }
            },
            goToPage(page) {
                if (page === '...' || page === this.pageIndex) return
                this.pageIndex = page
                this.getList()
            },
            // 前往指定页码
            gotoPage() {
                const page = parseInt(this.gotoPageInput)
                if (page >= 1 && page <= this.totalPages && page !== this.pageIndex) {
                    this.pageIndex = page
                    this.getList()
                } else {
                    uni.showToast({
                        title: '页码超出范围',
                        icon: 'none'
                    })
                }
            },
			async goToDetail(item) {
				console.log('跳转到详情页面:', item)

                // 只有当工单状态为 TODO 或 DOING 时才需要通过WebSocket检查占用
                if (item.state !== 'TODO' && item.state !== 'DOING') {
                    // 其他状态（DONE、CANCELED等）直接跳转
                    this.navigateToDetail(item);
                    return;
                }

                // 统一显示加载提示
                uni.showLoading({
                    title: '加载中...'
                });

                // 保存当前工单信息
                this.currentWorkOrderId = item.workOrderId;
                this.currentWorkOrderItem = item;
                this.isOpeningOrder = true;

                try {
                    // 为当前工单建立WebSocket连接
                    await this.initWebSocketForOrder(item.workOrderId);

                    // 连接成功后发送打开工单请求
                    this.sendOpenOrderRequest(item.workOrderId);
                    this.setOpenOrderTimeout();

                } catch (error) {
                    console.error('WebSocket连接失败:', error);
                    uni.hideLoading();
                    this.isOpeningOrder = false;
                    
                    // 显示错误信息
                    uni.showToast({
                        title: '连接失败，请重试',
                        icon: 'none'
                    });
                }
			},
			async cancelOrder(item) {
				console.log('撤销工单:', item)
				uni.showModal({
					title: '确认撤销',
					content: '确定要撤销此工单吗？',
					success: async (res) => {
						if (res.confirm) {
							try {
								uni.showLoading({
									title: '撤销中...'
								})
								
								await updateWorkOrderState({
									workOrderId: item.workOrderId,
									state: 'CANCELED'
								})
								
								uni.hideLoading()
								uni.showToast({
									title: '撤销成功',
									icon: 'success'
								})
								
								// 刷新列表数据
								await this.getList()
							} catch (error) {
								uni.hideLoading()
								console.error('撤销工单失败:', error)
								uni.showToast({
									title: '撤销失败',
									icon: 'none'
								})
							}
						}
					}
				})
			},
            async completeOrder(item) {
                console.log('完成工单:', item)
                uni.showModal({
                    title: '确认完成',
                    content: '确定要完成此工单吗？',
                    success: async (res) => {
                        if (res.confirm) {
                            try {
                                uni.showLoading({
                                    title: '完成中...'
                                })
                                
                                await updateWorkOrderState({
                                    workOrderId: item.workOrderId,
                                    state: 'DONE'
                                })
                                
                                uni.hideLoading()
                                uni.showToast({
                                    title: '完成成功',
                                    icon: 'success'
                                })
                                
                                // 刷新列表数据
                                await this.getList()
                            } catch (error) {
                                uni.hideLoading()
                                console.error('完成工单失败:', error)
                                uni.showToast({
                                    title: '完成失败',
                                    icon: 'none'
                                })
                            }
                        }
                    }
                })
            },
            async resubmitOrder(item) {
                console.log('重新提交工单:', item)
                uni.showModal({
                    title: '确认重新提交',
                    content: '确定要重新提交此工单吗？',
                    success: async (res) => {
                        if (res.confirm) {
                            try {
                                uni.showLoading({
                                    title: '重新提交中...'
                                })
                                
                                await reSubmitWorkOrder({
                                    workorderId: item.workOrderId
                                })
                                
                                uni.hideLoading()
                                uni.showToast({
                                    title: '重新提交成功',
                                    icon: 'success'
                                })
                                
                                // 刷新列表数据
                                await this.getList()
                            } catch (error) {
                                uni.hideLoading()
                                console.error('重新提交工单失败:', error)
                                uni.showToast({
                                    title: '重新提交失败',
                                    icon: 'none'
                                })
                            }
                        }
                    }
                })
            },
            
            // ========== WebSocket相关方法 ==========

            // 初始化设备ID
            async initDeviceId() {
                try {
                    // 初始化用户store
                    this.userStore = useUserStore();

                    // 获取设备ID
                    this.deviceId = await getOrGenerateDeviceId();
                    console.log('设备ID初始化完成:', this.deviceId);
                } catch (error) {
                    console.error('设备ID初始化失败:', error);
                }
            },

            // 为特定工单建立WebSocket连接
            async initWebSocketForOrder(workOrderId) {
                return new Promise((resolve, reject) => {
                    try {
                        // 检查必要参数
                        if (!workOrderId) {
                            reject(new Error('workOrderId不能为空'));
                            return;
                        }

                        if (!this.deviceId) {
                            reject(new Error('设备ID未初始化'));
                            return;
                        }

                        // 获取token
                        let token = this.userStore?.userInfo?.token;
                        if (!token) {
                            token = uni.getStorageSync('token');
                        }

                        if (!token) {
                            reject(new Error('未找到token'));
                            return;
                        }
                    
                        // 清理之前的连接
                        if (this.workOrderWS) {
                            this.workOrderWS.closeSocket();
                        }

                        // 建立连接 - 直接使用wsUrl，工单WebSocket类会自动转换为workOrderLink地址
                        const baseWsUrl = wsUrl;

                        // 创建WebSocket实例
                        this.workOrderWS = new WorkOrderWebSocketClass();

                        // 设置消息回调
                        this.workOrderWS.setMessageCallback(this.handleWebSocketMessage);

                        console.log(`准备为工单 ${workOrderId} 建立WebSocket连接`, {
                            baseWsUrl,
                            deviceId: this.deviceId,
                            hasToken: !!token,
                            tokenLength: token ? token.length : 0
                        });
                    
                        this.wsConnecting = true;

                        // 创建WebSocket连接，传递正确的workOrderId
                        this.workOrderWS.createWebSocket(
                            baseWsUrl,
                            token,
                            this.deviceId,
                            workOrderId, // 传递正确的工单ID
                            'PAD'
                        );

                        // 设置连接状态监听器
                        let checkCount = 0;
                        const maxCheckCount = 20; // 最多检查20次，总共10秒
                        
                        const checkConnection = () => {
                            checkCount++;
                            const status = this.workOrderWS?.getConnectionStatus();
                            
                            if (status?.isConnected) {
                                this.wsConnected = true;
                                this.wsConnecting = false;
                                console.log(`工单 ${workOrderId} 的WebSocket连接成功`);
                                resolve(true);
                            } else if (status?.reconnectAttempts >= 3 || checkCount >= maxCheckCount) {
                                this.wsConnecting = false;
                                console.log(`工单 ${workOrderId} 的WebSocket连接失败，重连次数: ${status?.reconnectAttempts}, 检查次数: ${checkCount}`);
                                reject(new Error('WebSocket连接失败，请检查网络连接'));
                            } else {
                                // 继续检查
                                setTimeout(checkConnection, 500);
                            }
                        };

                        // 开始检查连接状态
                        setTimeout(checkConnection, 1000);
                    
                    } catch (error) {
                        console.error(`工单 ${workOrderId} 的WebSocket初始化失败:`, error);
                        this.wsConnecting = false;
                        reject(error);
                    }
                });
            },
            
            // 处理WebSocket消息
            handleWebSocketMessage(messageData) {
                console.log('收到WebSocket消息:', messageData);
                
                if (!messageData || !messageData.type) {
                    console.log('消息格式无效，忽略处理');
                    return;
                }
                
                // 只有在正在打开工单的状态下才处理消息
                if (!this.isOpeningOrder) {
                    console.log('当前不在打开工单状态，忽略消息');
                    return;
                }
                
                switch (messageData.type) {
                    case MESSAGE_TYPES.CAN_OPEN_ORDER:
                        // 可以打开工单，直接跳转
                        this.handleCanOpenOrder();
                        break;
                        
                    case MESSAGE_TYPES.NEED_CONFIRM_SWITCH:
                        // 需要确认切换工单
                        this.handleNeedConfirmSwitch();
                        break;
                        
                    default:
                        console.log('未处理的消息类型:', messageData.type);
                        // 对于未知消息类型，如果是错误相关的，进行错误处理
                        if (messageData.type && messageData.type.includes('ERROR')) {
                            this.handleWebSocketError(messageData.message || '操作失败');
                        }
                        break;
                }
            },
            
            // 发送打开工单请求
            sendOpenOrderRequest(workOrderId) {
                if (!this.workOrderWS || !this.wsConnected) {
                    console.error('WebSocket未连接，无法发送消息');
                    this.handleWebSocketError('连接已断开，请重试');
                    return false;
                }
                
                // 打印当前连接的header参数状态
                if (this.workOrderWS.connectionParams && this.workOrderWS.connectionParams.headers) {
                    console.log('=== 发送工单请求时Header参数状态 ===');
                    console.log('当前连接Header参数:', JSON.stringify(this.workOrderWS.connectionParams.headers, null, 2));
                    console.log('请求的工单ID:', workOrderId);
                    console.log('=== 发送工单请求时Header参数状态结束 ===');
                }
                
                const success = this.workOrderWS.sendMessage({
                    type: MESSAGE_TYPES.OPEN_ORDER_REQUEST,
                    workOrderId: workOrderId,
                    message: '',
                    passwordLast4: ''
                });
                
                if (!success) {
                    console.error('发送打开工单请求失败');
                    this.handleWebSocketError('发送请求失败，请重试');
                    return false;
                }
                
                console.log('打开工单请求已发送，等待服务器响应...');
                return success;
            },
            
            // 发送强制切换消息
            sendCloseOrder(workOrderId) {
                if (!this.workOrderWS || !this.wsConnected) {
                    console.error('WebSocket未连接，无法发送消息');
                    return false;
                }
                
                const success = this.workOrderWS.sendMessage({
                    type: MESSAGE_TYPES.CLOSE_ORDER,
                    workOrderId: workOrderId,
                    message: '',
                    passwordLast4: ''
                });
                
                if (success) {
                    console.log('强制切换请求已发送，等待服务器响应...');
                } else {
                    console.error('发送强制切换请求失败');
                }
                
                return success;
            },
            
            // 处理可以打开工单
            handleCanOpenOrder() {
                this.clearOpenOrderTimeout();
                uni.hideLoading();
                this.isOpeningOrder = false;
                
                if (this.currentWorkOrderItem) {
                    this.navigateToDetail(this.currentWorkOrderItem);
                }
            },
            
            // 处理需要确认切换
            handleNeedConfirmSwitch() {
                this.clearOpenOrderTimeout();
                uni.hideLoading();
                this.isOpeningOrder = false;
                
                // 显示设备冲突确认弹窗
                this.showDeviceConflictModal = true;
            },
            
            // 设备冲突弹窗处理
            handleDeviceConflictCancel() {
                this.showDeviceConflictModal = false;
                this.currentWorkOrderId = '';
                this.currentWorkOrderItem = null;
            },
            
            async handleDeviceConflictConfirm(password) {
                try {
                    uni.showLoading({
                        title: '验证中...'
                    });
                    
                    // 调用HTTP接口验证密码
                    const result = await verifyPassword({
                        workOrderId: this.currentWorkOrderId,
                        passwordLast4: password
                    });
                    
                    if (result.code === '0000' && result.data && result.data.checkResult) {
                        // 密码正确，发送CLOSE_ORDER强制切换
                        const sendSuccess = this.sendCloseOrder(this.currentWorkOrderId);
                        
                        if (sendSuccess) {
                            // 更新loading提示
                            uni.showLoading({
                                title: '加载中...'
                            });
                            
                            // 设置超时处理，等待CAN_OPEN_ORDER响应
                            this.setOpenOrderTimeout();
                            
                            // 关闭弹窗
                            this.showDeviceConflictModal = false;
                        } else {
                            // 发送失败
                            this.handleWebSocketError('发送请求失败，请重试');
                            this.showDeviceConflictModal = false;
                        }
                    } else {
                        // 密码错误
                        uni.hideLoading();
                        if (this.$refs.deviceConflictModal) {
                            this.$refs.deviceConflictModal.setError('密码验证失败，请重新输入');
                        }
                    }
                } catch (error) {
                    uni.hideLoading();
                    console.error('密码验证失败:', error);
                    if (this.$refs.deviceConflictModal) {
                        this.$refs.deviceConflictModal.setError('网络错误，请重试');
                    }
                }
            },
            
            // 直接跳转到详情页面
            navigateToDetail(item) {
                uni.navigateTo({
                    url: `/pages/assessment/detail?workOrderId=${item.workOrderId}&traineeId=${item.traineeId || ''}`,
                    success: (res) => {
                        console.log('跳转详情页面成功', res);
                    },
                    fail: (err) => {
                        console.log('跳转详情页面失败', err);
                        uni.showToast({
                            title: '页面跳转失败',
                            icon: 'none'
                        });
                    }
                });
            },
            
            // 设置打开工单超时处理
            setOpenOrderTimeout() {
                this.clearOpenOrderTimeout();
                this.openOrderTimeout = setTimeout(() => {
                    if (this.isOpeningOrder) {
                        console.log('打开工单请求超时');
                        this.handleWebSocketError('请求超时，请重试');
                    }
                }, 10000); // 10秒超时，给WebSocket更多时间
            },
            
            // 清除打开工单超时
            clearOpenOrderTimeout() {
                if (this.openOrderTimeout) {
                    clearTimeout(this.openOrderTimeout);
                    this.openOrderTimeout = null;
                }
            },
            
            // 处理WebSocket错误
            handleWebSocketError(errorMessage) {
                console.error('WebSocket操作错误:', errorMessage);
                this.clearOpenOrderTimeout();
                uni.hideLoading();
                this.isOpeningOrder = false;
                
                uni.showToast({
                    title: errorMessage || '操作失败，请重试',
                    icon: 'none'
                });
                
                // 清理当前工单状态
                this.currentWorkOrderId = '';
                this.currentWorkOrderItem = null;
            },
            
            // 清理WebSocket连接
            cleanupWebSocket() {
                if (this.workOrderWS) {
                    console.log('清理WebSocket连接');
                    this.workOrderWS.closeSocket();
                    this.workOrderWS = null;
                }
                this.wsConnected = false;
                this.wsConnecting = false;
                this.clearOpenOrderTimeout();
            }
		}
	}
</script>

<style lang="scss" scoped>
@import '@/styles/responsive.scss';
	.assessment-page {
		width: 100vw;
		height: 100vh;
		min-height: 100vh;
		background: #F6F6F6;
		display: flex;
		flex-direction: column;
		overflow: hidden;
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 1000;
	}
	
	/* 状态栏 */
	.status-bar {
		width: 100%;
		height: 44px;
		background: #FFFFFF;
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 0 20px;
		flex-shrink: 0;
		
		.time {
			font-family: 'Alibaba PuHuiTi';
			font-size: 26px;
			color: #333333;
		}
		
		.status-icons {
			display: flex;
			align-items: center;
			gap: 50px;
		}
	}
	
	/* 顶部功能区 */
	.header {
		position: relative;
		width: 100%;
		height: 88px;
		flex-shrink: 0;
		
		.header-bg {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: #FFFFFF;
		}
		
		.back-btn {
			position: absolute;
			left: 40px;
			top: 28px;
			width: 32px;
			height: 32px;
			display: flex;
			align-items: center;
			justify-content: center;
			
			.back-icon {
				width: 24px;
				height: 24px;
			}
		}
		
		.title {
			position: absolute;
			left: 50%;
			top: 28px;
			transform: translateX(-50%);
			font-family: 'Alibaba PuHuiTi';
			font-size: 32px;
			color: #333333;
			line-height: 32px;
		}
		
		.button-group {
			position: absolute;
			right: 37px;
			top: 17px;
			padding: 15px;
			border: 1px solid #EEEEEE;
			border-radius: 7px;
			background: #FFFFFF;
			
			.btn-new-order {
				display: flex;
				align-items: center;
				gap: 7px;
				cursor: pointer;
				min-width: 80px;
				min-height: 30px;
				transition: opacity 0.2s ease;
				
				&:active {
					opacity: 0.7;
				}
				
				&.btn-loading {
					opacity: 0.6;
					cursor: not-allowed;
				}
				
				.btn-icon {
					width: 19px;
					height: 19px;
					display: flex;
					align-items: center;
					justify-content: center;
					border: 1px solid #333333;
					border-radius: 15px;
					
					.icon-plus {
						width: 7px;
						height: 7px;
						position: relative;
						
						&::before {
							content: '';
							position: absolute;
							top: 50%;
							left: 50%;
							transform: translate(-50%, -50%);
							width: 7px;
							height: 1px;
							background: #333333;
						}
						
						&::after {
							content: '';
							position: absolute;
							top: 50%;
							left: 50%;
							transform: translate(-50%, -50%);
							width: 1px;
							height: 7px;
							background: #333333;
						}
					}
				}
				
				.btn-text {
					font-family: 'Alibaba PuHuiTi';
					font-size: 19px;
					color: #333333;
					line-height: 19px;
				}
				
				.loading-icon {
					width: 19px;
					height: 19px;
					display: flex;
					align-items: center;
					justify-content: center;
					font-size: 12px;
					color: #999999;
					animation: loading 1s infinite;
				}
			}
		}
	}
	
	@keyframes loading {
		0% { opacity: 0.3; }
		50% { opacity: 1; }
		100% { opacity: 0.3; }
	}

        /* 浮窗日历样式 */
        .date-popover {
            position: fixed;
            z-index: 9999;
            width: 280px;
            background: #FFFFFF;
            border: 1px solid #EEEEEE;
            border-radius: 8px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
            padding: 10px 10px 14px;
            /* 位置通过内联样式动态设置 */
        }
        .date-popover .popover-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
        }
        .date-popover .nav-btn {
            width: 28px;
            height: 28px;
            border-radius: 4px;
            border: 1px solid #EEEEEE;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        .date-popover .ym-text {
            font-size: 16px;
            color: #333333;
        }
        .date-popover .week-row {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 6px;
            margin-bottom: 6px;
        }
        .date-popover .week-cell {
            text-align: center;
            font-size: 12px;
            color: #999999;
        }
        .date-popover .day-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 6px;
        }
        .date-popover .day-cell {
            height: 34px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            color: #333333;
        }
        .date-popover .day-cell.other { color: #C7C7C7; }
        .date-popover .day-cell.selected { background: #287FFF; color: #FFFFFF; }
	
	/* 表头筛选区 */
	.filter-header {
		width: calc(100% - 80px);
		margin: 20px 40px 0 40px;
		background: #FFFFFF;
		border-radius: 8px;
		display: flex;
		align-items: center;
		padding: 15px 22px;
		gap: 22px;
		border-bottom: 1px solid #EEEEEE;
		flex-shrink: 0;
		position: relative; /* 为浮窗提供定位基准 */
		
		.search-frame {
			display: flex;
			align-items: center;
			border: 1px solid #C7C7C7;
			border-radius: 7px;
			background: #FFFFFF;
			padding-left: 15px;
			
			.search-input {
				height: 44px;
				min-width: 280px;
				font-family: 'Alibaba PuHuiTi';
				font-size: 22px;
				color: #333333;
				border: none;
				outline: none;
				background: transparent;
				
				&::placeholder {
					color: #999999;
					font-size: 22px;
				}
			}
			
			.search-addon {
				width: 44px;
				height: 44px;
				display: flex;
				align-items: center;
				justify-content: center;
				border-left: 1px solid #EEEEEE;
				border-radius: 0 4px 4px 0;
				
				.search-icon {
					width: 21px;
					height: 21px;
					position: relative;
					
					&::before {
						content: '';
						position: absolute;
						top: 3px;
						left: 3px;
						width: 12px;
						height: 12px;
						border: 2px solid #999999;
						border-radius: 50%;
					}
					
					&::after {
						content: '';
						position: absolute;
						bottom: 2px;
						right: 2px;
						width: 6px;
						height: 2px;
						background: #999999;
						transform: rotate(45deg);
						border-radius: 1px;
					}
				}
			}
		}
		
		.filter-select {
			width: 202px;
			background: #FFFFFF;
			border: 1px solid #EEEEEE;
			border-radius: 4px;
			padding: 11px 15px;
			display: flex;
			align-items: center;
			justify-content: space-between;
			gap: 7px;
            position: relative;
			
			.filter-text {
				font-family: 'Alibaba PuHuiTi';
				font-size: 22px;
				color: #999999;
				line-height: 22px;
                &.active { color: #333333; }
			}
			
			.arrow-down {
				width: 22px;
				height: 22px;
				position: relative;
				
				&::after {
					content: '';
					position: absolute;
					top: 50%;
					left: 50%;
					transform: translate(-50%, -50%);
					width: 14px;
					height: 8px;
					background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 14 8'%3E%3Cpath d='M1 1l6 6 6-6' stroke='%23999999' stroke-width='2' fill='none'/%3E%3C/svg%3E") no-repeat center;
					background-size: contain;
				}
			}
            .dropdown {
                position: absolute;
                top: 100%;
                left: 0;
                width: 100%;
                background: #FFFFFF;
                border: 1px solid #EEEEEE;
                border-radius: 4px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.06);
                margin-top: 7px;
                z-index: 20;
                .dropdown-item {
                    padding: 10px 12px;
                    font-size: 20px;
                    color: #333333;
                    &.selected { color: #287FFF; }
                    &:active { background: #F6F6F6; }
                }
            }
		}
		
		.date-picker {
			background: #FFFFFF;
			border: 1px solid #EEEEEE;
			border-radius: 4px;
			padding: 11px 15px;
			display: flex;
			align-items: center;
			gap: 7px;
			cursor: pointer;
			transition: background 0.2s ease, border-color 0.2s ease;
			&:active { background: #F6F6F6; border-color: #DADADA; }
			
			.date-text {
				font-family: 'Alibaba PuHuiTi';
				font-size: 22px;
				color: #999999;
				line-height: 22px;
                &.active { color: #333333; }
			}
			
			.calendar-icon-wrapper {
				width: 22px;
				height: 22px;
				display: flex;
				align-items: center;
				justify-content: center;
				
				.calendar-icon {
					width: 18px;
					height: 18px;
				}
			}
		}
		
		.button-group-right {
			margin-left: auto;
			display: flex;
			gap: 11px;
			
			.btn-search {
				padding: 15px 22px;
				background: #287FFF;
				border-radius: 7px;
				font-family: 'Alibaba PuHuiTi';
				font-size: 22px;
				color: #FFFFFF;
				line-height: 20px;
				text-align: center;
			}
			
			.btn-reset {
				padding: 15px 22px;
				background: #FFFFFF;
				border: 1px solid #C7C7C7;
				border-radius: 7px;
				font-family: 'Alibaba PuHuiTi';
				font-size: 22px;
				color: #333333;
				line-height: 20px;
				text-align: center;
			}
		}
	}
	
	/* 表格容器 */
	.table-container {
		margin: 0 40px;
		background: #FFFFFF;
		border-radius: 0 0 8px 8px;
		margin-top: -1px;
		flex: 1;
		display: flex;
		flex-direction: column;
		min-height: 0;
		overflow: hidden;
	}
	
	.table-wrapper {
		flex: 1;
		display: flex;
		flex-direction: column;
		min-height: 0;
	}
	
	.table-header {
		display: flex;
		background: #EEEEEE;
		flex-shrink: 0;
		
		.header-cell {
			font-family: 'Alibaba PuHuiTi';
			font-weight: 400;
			font-size: 22px;
			color: #333333;
			background: #EEEEEE;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 15px 11px;
			border-bottom: 1px solid #EEEEEE;
			border-right: 1px solid #EEEEEE;
			
			&:last-child {
				border-right: none;
			}
		}
	}
	
	.table-body {
		flex: 1;
		overflow-y: auto;
		min-height: 0;
		
		.table-row {
			display: flex;
			
			&:hover {
				background: #F8F9FA;
			}
		}
	}
	
	.table-cell {
		font-family: 'Alibaba PuHuiTi';
		font-size: 22px;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 8px 11px;
		border-bottom: 1px solid #EEEEEE;
		border-right: 1px solid #EEEEEE;
		min-height: 61px;
		
		&:last-child {
			border-right: none;
		}
	}
	
	/* 列宽设置 - 按Figma比例 */
	.col-index {
		width: 4.37%; /* 59.03/1350.7 */
	}
	
	.col-patient {
		width: 22.93%; /* 309.72/1350.7 */
		justify-content: flex-start;
		
		.patient-info {
			text-align: left;
			
			.patient-name {
				font-size: 22px;
				color: #333333;
				margin-bottom: 2px;
			}
			
			.patient-details {
				font-size: 20px;
				color: #666666;
			}
		}
	}
	
	.col-order {
		width: 20.72%; /* 279.86/1350.7 */
	}
	
	.col-time {
		width: 20.10%; /* 271.53/1350.7 */
	}
	
	.col-status {
		width: 13.99%; /* 188.89/1350.7 */
		justify-content: flex-start;
		padding-left: 20px;
	}
	
.status-wrapper {
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

	.col-action {
		width: 17.89%; /* 241.67/1350.7 */
		justify-content: flex-start;
		padding-left: 20px;
	}
	
	.status-tag {
		padding: 9px 11px;
		border-radius: 4px;
		font-size: 20px;

    /* Figma 对齐：四种状态样式 */
    &.TODO { /* 待执行 */
        background: #FFE7E7;
        color: #FF4D50;
    }
    &.DOING { /* 执行中 */
        background: #D7FFEF;
        color: #20DF92;
    }
    &.DONE { /* 已完成 */
        background: #EBF2FF;
        color: #287FFF;
    }
    &.CANCELED { /* 已撤销 */
        background: #FFEFD2;
        color: #FFB130;
    }
	}

.progress-tag {
    padding: 9px 11px;
    border-radius: 4px;
    font-size: 20px;
    background: #F5F5F5; /* 进度标签统一灰底，可按 Figma 调整 */
    color: #666666;
}
	
	.action-btns {
		display: flex;
		align-items: center;
		width: 100%;
		height: 100%;
		
		.action-btn {
			display: flex;
			align-items: center;
			gap: 7px;
			width: 100%;
			justify-content: flex-start;
			padding: 0;
			
			.btn-text {
				font-family: 'Alibaba PuHuiTi';
				font-size: 22px;
				color: #287FFF;
				line-height: 22px;
				cursor: pointer;
				background: transparent;
				
				&:hover {
					text-decoration: underline;
				}
			}
			
			.btn-divider {
				width: 1px;
				height: 11px;
				background: #EEEEEE;
				flex-shrink: 0;
			}
		}
	}
	
	/* 分页 */
	.pagination {
		display: flex;
		align-items: center;
		padding: 11px 15px;
		border-top: 1px solid #EEEEEE;
		flex-shrink: 0;
		
		.pagination-left {
			.total-text {
				font-family: 'Alibaba PuHuiTi';
				font-size: 22px;
				color: #333333;
			}
		}
		
		.pagination-controls {
			display: flex;
			align-items: center;
			gap: 7px;
			margin-left: auto;
			margin-right: 15rpx;
			
			.page-size-selector {
				display: flex;
				align-items: center;
				gap: 2px;
				padding: 7px;
				border: 1px solid #EEEEEE;
				border-radius: 4px;
				background: #FFFFFF;
				
				.page-size-text {
					font-family: 'Alibaba PuHuiTi';
					font-size: 22px;
					color: #333333;
				}
				
				.page-size-arrow {
					width: 15px;
					height: 15px;
					position: relative;
					
					&::after {
						content: '';
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
						width: 10px;
						height: 6px;
						background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 10 6'%3E%3Cpath d='M1 1l4 4 4-4' stroke='%23333333' stroke-width='1' fill='none'/%3E%3C/svg%3E") no-repeat center;
						background-size: contain;
					}
				}
			}
			
			.page-btn {
				width: 37px;
				height: 37px;
				border: 1px solid #EEEEEE;
				border-radius: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				background: #FFFFFF;
				
				&.disabled {
					opacity: 0.4;
					cursor: not-allowed;
				}
				
				.arrow-left,
				.arrow-right {
					width: 15px;
					height: 15px;
					position: relative;
					
					&::after {
						content: '';
						position: absolute;
						top: 50%;
						left: 50%;
						transform: translate(-50%, -50%);
						width: 8px;
						height: 8px;
						border: 2px solid #333333;
						border-bottom: none;
						border-right: none;
					}
				}
				
				.arrow-left::after {
					transform: translate(-30%, -50%) rotate(-45deg);
				}
				
				.arrow-right::after {
					transform: translate(-70%, -50%) rotate(135deg);
				}
			}
			
			.page-numbers {
				display: flex;
				align-items: center;
				gap: 7px;
				
				.page-number {
					width: 37px;
					height: 37px;
					border: 1px solid #EEEEEE;
					border-radius: 4px;
					display: flex;
					align-items: center;
					justify-content: center;
					font-family: 'Alibaba PuHuiTi';
					font-size: 22px;
					cursor: pointer;
					background: #FFFFFF;
					color: #333333;
					
					&.active {
						background: #FFFFFF;
						color: #287FFF;
						border-color: #287FFF;
					}
				}
				
				.page-ellipsis {
					padding: 0 5px;
					font-size: 22px;
					color: #999999;
				}
			}
		}
		
		.pagination-right {
			display: flex;
			align-items: center;
			gap: 5px;
			
			.goto-text {
				font-family: 'Alibaba PuHuiTi';
				font-size: 22px;
				color: #333333;
			}
			
			.goto-input {
				width: 37px;
				height: 37px;
				border: 1px solid #EEEEEE;
				border-radius: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				background: #FFFFFF;
				
				.goto-input-field {
					width: 100%;
					height: 100%;
					border: none;
					background: transparent;
					text-align: center;
					font-family: 'Alibaba PuHuiTi';
					font-size: 22px;
					color: #333333;
				}
			}
		}
	}
</style>