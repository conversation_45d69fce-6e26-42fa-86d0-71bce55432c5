<template>
	<view class="assessment-detail-page">
		<!-- 状态栏 -->
		<view class="status-bar">
		</view>
		
		<!-- 顶部功能区 -->
		<view class="header">
			<view class="header-bg"></view>
			<text class="patient-info">{{ patientInfo }}</text>
			<view class="refresh-btn" @click="refreshData">
				<view class="refresh-icon"></view>
			</view>
			<text class="refresh-text" @click="refreshData">刷新</text>
			<text class="instruction">{{ instructionText }}</text>
			<view class="exit-btn" @click="exitAssessment">
				<view class="exit-icon"></view>
				<text class="exit-text">退出测评</text>
			</view>
		</view>
		
		<!-- 选项卡组合 -->
		<view class="tab-container">
			<view class="tab-group">
				<view class="tab-item" 
					v-for="(group, index) in respondentGroups" 
					:key="group.respondentGroupType"
					:class="{ active: activeTabIndex === index }" 
					@click="switchTab(index)">
					<text class="tab-text">{{ group.respondentGroupName }}（{{ group.process }}）</text>
				</view>
			</view>
			
			<!-- 二维码按钮 -->
			<view class="qr-code-section" v-if="showQRCodeButton">
				<view class="qr-code-btn" @click="showQRCode">
					<image src="/static/figma-assets/qr-scan.png" class="qr-icon"></image>
					<text class="qr-text">{{ qrCodeButtonText }}</text>
				</view>
			</view>
		</view>
		
		<!-- 表格 -->
		<view class="table-container">
			<!-- 表格头部 -->
			<view class="table-header">
				<view class="table-cell header-cell col-index">序号</view>
				<view class="table-cell header-cell col-project">项目</view>
				<view class="table-cell header-cell col-status">完成情况</view>
				<view class="table-cell header-cell col-score">得分</view>
				<view class="table-cell header-cell col-action">操作</view>
			</view>
			
			<!-- 表格内容 -->
			<view class="table-body">
				<view class="table-row" v-for="(item, index) in currentTabData" :key="item.projectId">
					<view class="table-cell col-index">{{ index + 1 }}</view>
					<view class="table-cell col-project">
						<view class="project-info">
							<text class="project-name">{{ item.projectName }}</text>
							<view class="project-tag" :class="getProjectTagClass(item.quizType)">
								<text class="tag-text">{{ getProjectTagText(item.quizType) }}</text>
							</view>
						</view>
					</view>
					<view class="table-cell col-status">
						<view class="status-tag" :class="getProjectStatusClass(item.state)">
							<text class="status-text">{{ getProjectStatusText(item.state) }}</text>
						</view>
					</view>
					<view class="table-cell col-score">{{ item.score || '--' }}</view>
					<view class="table-cell col-action">
						<view class="action-btns">
							<!-- 如果工单状态为已完成或已撤销，所有项目只显示详情按钮 -->
							<text class="action-btn" @click="viewDetail(item, $event)" v-if="workOrderState === 'DONE' || workOrderState === 'CANCELED'">详情</text>
							<!-- 正常状态下的操作按钮 -->
							<template v-else>
								<text class="action-btn" @click="viewDetail(item, $event)" v-if="item.state === '1'">详情</text>
								<view class="btn-divider" v-if="item.state === '1'"></view>
								<text class="action-btn" @click="retakeAssessment(item)" v-if="item.state === '1'">重新测评</text>
								<text class="action-btn" @click="startAssessment(item)" v-if="item.state === '0'">开始测评</text>
							</template>
						</view>
					</view>
				</view>
			</view>
		</view>
		
						<!-- 二维码弹窗 -->
		<view class="qr-modal" v-if="showQRModal" @click="closeQRModal">
			<view class="qr-modal-content" @click.stop>
				<view class="qr-content-wrapper">
					<!-- 二维码图片区域 -->
					<view class="qr-image-section">
						<!-- 正常状态显示二维码 -->
						<image
							v-if="!isQRExpired"
							:src="qrCodeImageSrc"
							class="qr-code-image"
						></image>
						<!-- 失效状态显示提示框 -->
						<view v-else class="qr-expired-placeholder">
							<view class="expired-icon">!</view>
							<text class="expired-text">二维码已失效</text>
						</view>
					</view>

					<!-- 文字说明区域 -->
					<view class="qr-text-section">
						<text class="qr-main-text">{{ qrMainText }}</text>
						<text class="qr-sub-text" :class="{ expired: isQRExpired }">{{ qrSubText }}</text>
					</view>
				</view>

				<!-- 按钮区域 -->
				<view class="qr-button-section">
					<view class="qr-button close-btn" @click="closeQRModal">
						<text class="button-text">关闭</text>
					</view>
					<view class="qr-button regenerate-btn" @click="regenerateQRCode">
						<text class="button-text">重新生成</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 报告选项浮窗 -->
		<view class="report-modal" v-if="showReportModal" @click="closeReportModal">
			<view class="report-modal-content" @click.stop :style="{ top: reportModalPosition.top + 'px', left: reportModalPosition.left + 'px' }">
				<view class="report-option-item" 
					v-for="(option, index) in reportOptions" 
					:key="index"
					@click="selectReportOption(option)">
					<text class="option-text">{{ option.name }}</text>
				</view>
			</view>
		</view>

		<!-- 退出确认弹窗 -->
		<ExitConfirmModal
			ref="exitConfirmModal"
			:visible="showExitConfirmModal"
			:loading="exitConfirmLoading"
			@cancel="handleExitConfirmCancel"
			@confirm="handleExitConfirmConfirm"
			@maskClick="handleExitConfirmCancel"
		/>
		
		<!-- 报告文档选择弹窗 -->
		<ReportDocumentModal
			:visible="showReportModal"
			:reportData="currentReportData"
			@close="closeReportModal"
			@openDocument="openReportDocument"
		/>
	</view>
</template>

<script>
	import { qryWorkOrderDetailInfo, generalWorkOrderQrCode, qryWorkOrderQrCodeInfo, verifyPassword, exportProjectEvaluationPdf } from '@/service/workorder.js';
	import WorkOrderWebSocketClass, { MESSAGE_TYPES } from '@/utils/workOrderWebSocket.js';
	import { getOrGenerateDeviceId } from '@/utils/deviceUtils.js';
	import { useUserStore } from '@/stores/user.js';
	import { wsUrl } from '@/common/global.js';
	import ExitConfirmModal from '@/components/ExitConfirmModal.vue';
	import ReportDocumentModal from '@/components/ReportDocumentModal.vue';

	export default {
		components: {
			ExitConfirmModal,
			ReportDocumentModal
		},
		data() {
			return {
				workOrderId: '',
				traineeId: '', // 患者ID
				activeTabIndex: 0,
				patientInfo: '',
				instructionText: '请认真完成以下测评项目',
				workOrderState: '',
				assessmentSetInfo: {},
				respondentGroups: [],
				loading: false,
				// 二维码弹窗相关
				showQRModal: false,
				isQRExpired: false, // false: 正常状态, true: 失效状态
				currentQRType: '', // 'T': 教师, 'P': 家长
				qrCodeBase64: '', // 二维码图片base64数据
				qrCodeExpireTime: '', // 二维码过期时间
				currentQRCodeId: '', // 当前二维码ID
				// WebSocket相关
				workOrderWS: null, // WebSocket实例
				deviceId: '', // 设备ID
				userStore: null, // 用户store
				// 弹窗状态
				showExitConfirmModal: false, // 退出确认弹窗
				// WebSocket连接状态
				wsConnected: false,
				wsConnecting: false,
				// 退出相关
				exitConfirmLoading: false,
				exitTimeout: null,
				// 报告文档相关
				showReportModal: false,
				currentReportData: {}
			}
		},
		onShow() {
			// 从测评页面返回后自动刷新工单详情
			if (this.workOrderId) {
				this.loadWorkOrderDetail(true); // 保持当前选中的标签页
			}
		},

		onUnload() {
			// 页面卸载时清理WebSocket连接
			this.cleanupWebSocket();
		},

		onHide() {
			// 页面隐藏时不关闭WebSocket，保持连接状态
		},
		computed: {
			currentTabData() {
				if (this.respondentGroups && this.respondentGroups[this.activeTabIndex]) {
					return this.respondentGroups[this.activeTabIndex].detailProjectInfoList || [];
				}
				return [];
			},
			
			// 是否显示二维码按钮
			showQRCodeButton() {
				const currentGroup = this.respondentGroups[this.activeTabIndex];
				if (!currentGroup) return false;
				// 只有教师部分(T)和家长部分(P)显示二维码按钮
				return currentGroup.respondentGroupType === 'T' || currentGroup.respondentGroupType === 'P';
			},
			
			// 二维码按钮文本
			qrCodeButtonText() {
				const currentGroup = this.respondentGroups[this.activeTabIndex];
				if (!currentGroup) return '';
				return currentGroup.respondentGroupType === 'T' ? '教师二维码' : '家长二维码';
			},
			
			// 二维码图片路径
			qrCodeImageSrc() {
				// 如果有真实的二维码数据，使用base64数据，否则使用默认图片
				if (this.qrCodeBase64) {
					return `data:image/png;base64,${this.qrCodeBase64}`;
				}
				return '/static/figma-assets/qr-code-normal.png';
			},
			
			// 二维码主要文本
			qrMainText() {
				return this.currentQRType === 'T' ? '教师微信扫一扫，手机端完成测评项目' : '家长微信扫一扫，手机端完成测评项目';
			},
			
			// 二维码副文本
			qrSubText() {
				if (this.isQRExpired) {
					return '(二维码已失效，请重新生成)';
				} else {
					const expireTime = this.qrCodeExpireTime || '2025年6月10日';
					return `(有效期至${expireTime})`;
				}
			}
		},
		async onLoad(options) {
			// 接收传递的参数
			if (options.workOrderId) {
				this.workOrderId = options.workOrderId;
			}
			// 如果从上个页面传入了 traineeId，优先记录，避免详情接口延迟前页面需要使用
			if (options.traineeId) {
				this.traineeId = options.traineeId;
			}

			// 初始化用户store
			this.userStore = useUserStore();

			// 获取设备ID
			try {
				this.deviceId = await getOrGenerateDeviceId();
				console.log('设备ID:', this.deviceId);
			} catch (error) {
				console.error('获取设备ID失败:', error);
				this.deviceId = 'fallback_' + Date.now();
			}

			// 加载工单详情
			await this.loadWorkOrderDetail();

			// 初始化简化的WebSocket连接（仅用于接收REQUIRE_EXIT消息）
			this.initSimpleWebSocket();
		},
		methods: {
			// 初始化简化的WebSocket连接（仅用于接收REQUIRE_EXIT消息）
			initSimpleWebSocket() {
				// 如果工单已完成或已撤销，不需要WebSocket连接
				if (this.workOrderState === 'DONE' || this.workOrderState === 'CANCELED') {
					return;
				}

				try {
					// 获取token
					let token = this.userStore?.userInfo?.token;
					if (!token) {
						token = uni.getStorageSync('token');
					}

					if (!token || !this.workOrderId || !this.deviceId) {
						console.log('缺少必要参数，跳过WebSocket初始化');
						return;
					}

					// 创建WebSocket实例
					this.workOrderWS = new WorkOrderWebSocketClass();

					// 设置消息回调
					this.workOrderWS.setMessageCallback(this.handleWebSocketMessage);

					// 建立连接 - 直接使用wsUrl，工单WebSocket类会自动转换为workOrderLink地址
					const baseWsUrl = wsUrl;
					this.wsConnecting = true;

					this.workOrderWS.createWebSocket(
						baseWsUrl,
						token,
						this.deviceId,
						this.workOrderId,
						'PAD'
					);

					// 监听连接状态
					setTimeout(() => {
						if (this.workOrderWS?.getConnectionStatus().isConnected) {
							this.wsConnected = true;
							this.wsConnecting = false;
							console.log('WebSocket连接成功');
						} else {
							this.wsConnecting = false;
							console.log('WebSocket连接失败');
						}
					}, 2000);

				} catch (error) {
					console.error('WebSocket初始化失败:', error);
					this.wsConnecting = false;
				}
			},

			// 处理WebSocket消息
			handleWebSocketMessage(messageData) {
				console.log('收到WebSocket消息:', messageData);

				if (!messageData || !messageData.type) {
					console.log('消息格式无效，忽略处理');
					return;
				}

				switch (messageData.type) {
					case MESSAGE_TYPES.REQUIRE_EXIT:
						// 要求退出
						console.log('收到退出要求');
						this.handleRequireExit();
						break;

					case MESSAGE_TYPES.ACTIVE_EXIT_SUCCESS:
						// 退出成功
						console.log('退出成功');
						this.handleExitSuccess();
						break;

					default:
						console.log('未处理的消息类型:', messageData.type);
						break;
				}
			},

			// 发送主动退出消息
			sendActiveExit() {
				if (!this.workOrderWS || !this.wsConnected) {
					console.error('WebSocket未连接，无法发送消息');
					return;
				}

				this.workOrderWS.sendMessage({
					type: MESSAGE_TYPES.ACTIVE_EXIT,
					workOrderId: this.workOrderId,
					message: '',
					passwordLast4: ''
				});
			},

			// 加载工单详情数据
			async loadWorkOrderDetail(keepCurrentTab = false) {
				if (!this.workOrderId) return;

				// 保存当前选中的标签页索引
				const currentTabIndex = this.activeTabIndex;

				this.loading = true;
				try {
					const response = await qryWorkOrderDetailInfo({
						workOrderId: this.workOrderId
					});

					if (response.code === '0000' && response.data) {
						const data = response.data;

						// 设置患者信息
						this.patientInfo = data.traineeSimDesc || '';
						// 优先保留从上个页面传入的 traineeId，避免被详情接口缺失字段覆盖
						this.traineeId = this.traineeId || data.traineeId || '';

						// 设置工单状态
						this.workOrderState = data.state;
						this.updateInstructionText(data.state);

						// 设置测评套件信息
						this.assessmentSetInfo = data.assessmentSetInfo || {};

						// 设置受试者组信息
						this.respondentGroups = data.respondentGroupInfoList || [];

						// 设置标签页选中状态
						if (this.respondentGroups.length > 0) {
							if (keepCurrentTab && currentTabIndex < this.respondentGroups.length) {
								// 保持当前选中的标签页
								this.activeTabIndex = currentTabIndex;
							} else {
								// 设置默认选中第一个标签页
								this.activeTabIndex = 0;
							}
						}
					}
				} catch (error) {
					console.error('加载工单详情失败:', error);
					uni.showToast({
						title: '加载失败',
						icon: 'none'
					});
				} finally {
					this.loading = false;
				}
			},
			
			// 更新顶部指示文字
			updateInstructionText(state) {
				switch (state) {
					case 'DONE':
						this.instructionText = '测评工单已完成';
						break;
					case 'CANCELED':
						this.instructionText = '测评工单已撤销';
						break;
					default:
						this.instructionText = '请认真完成以下测评项目';
						break;
				}
			},
			
			// 获取项目状态样式类
			getProjectStatusClass(state) {
				switch (state) {
					case '1':
						return 'DONE'; // 已完成
					case '0':
					default:
						return 'TODO'; // 待测评
				}
			},
			
			// 获取项目状态文本
			getProjectStatusText(state) {
				switch (state) {
					case '1':
						return '已完成';
					case '0':
					default:
						return '未完成';
				}
			},

			// 获取项目标签样式类
			getProjectTagClass(quizType) {
				switch (quizType) {
					case 'CPT-STD':
						return 'orange'; // IVA-CPT 标准版 - 橙色
					case 'CPT-MINI':
						return 'orange'; // IVA-CPT MINI版 - 橙色
					case 'PPVT':
						return 'green'; // PPVT - 绿色
					case 'QUEST':
					default:
						return 'blue'; // QUEST 和默认都是蓝色
				}
			},

			// 获取项目标签文本
			getProjectTagText(quizType) {
				switch (quizType) {
					case 'CPT-STD':
						return 'IVA-CPT标准版';
					case 'CPT-MINI':
						return 'IVA-CPT MINI';
					case 'PPVT':
						return 'PPVT测评';
					case 'QUEST':
					default:
						return '测评项目'; // QUEST 和默认都显示"测评项目"
				}
			},
			
			switchTab(tabIndex) {
				this.activeTabIndex = tabIndex;
			},
			
			refreshData() {
				this.loadWorkOrderDetail(true); // 保持当前选中的标签页
				uni.showToast({
					title: '刷新成功',
					icon: 'success'
				});
			},
			
			// 显示二维码
			async showQRCode() {
				const currentGroup = this.respondentGroups[this.activeTabIndex];
				if (!currentGroup) return;

				this.currentQRType = currentGroup.respondentGroupType;
				this.showQRModal = true;

				// 检查是否已有二维码ID
				if (currentGroup.qrCodeId) {
					// 有二维码ID，直接获取二维码信息
					await this.fetchQRCodeInfo(currentGroup.qrCodeId);
				} else {
					// 没有二维码ID，需要先生成二维码
					await this.generateQRCode(currentGroup.respondentGroupType);
				}
			},
			
			// 关闭二维码弹窗
			closeQRModal() {
				this.showQRModal = false;
				// 清理二维码相关数据
				this.qrCodeBase64 = '';
				this.qrCodeExpireTime = '';
				this.currentQRCodeId = '';
				this.isQRExpired = false;
				this.currentQRType = '';
			},
			
			// 重新生成二维码
			async regenerateQRCode() {
				if (!this.currentQRType) return;

				try {
					uni.showLoading({
						title: '生成中...'
					});

					// 重新生成二维码
					await this.generateQRCode(this.currentQRType);

					uni.hideLoading();
					uni.showToast({
						title: '二维码已重新生成',
						icon: 'success'
					});
				} catch (error) {
					uni.hideLoading();
					console.error('重新生成二维码失败:', error);
					uni.showToast({
						title: '生成失败',
						icon: 'none'
					});
				}
			},

			// 生成二维码
			async generateQRCode(respondentGroup) {
				try {
					const response = await generalWorkOrderQrCode({
						workOrderId: this.workOrderId,
						respondentGroup: respondentGroup
					});

					if (response.code === '0000') {
						// 生成成功，重新加载工单详情获取新的qrCodeId，保持当前选中的标签页
						await this.loadWorkOrderDetail(true);

						// 获取当前组的最新数据
						const currentGroup = this.respondentGroups.find(group =>
							group.respondentGroupType === respondentGroup
						);

						if (currentGroup && currentGroup.qrCodeId) {
							// 获取二维码信息
							await this.fetchQRCodeInfo(currentGroup.qrCodeId);
						}
					} else {
						throw new Error(response.msg || '生成失败');
					}
				} catch (error) {
					console.error('生成二维码失败:', error);
					throw error;
				}
			},

			// 获取二维码信息
			async fetchQRCodeInfo(qrCodeId) {
				try {
					const response = await qryWorkOrderQrCodeInfo({
						qrCodeId: qrCodeId
					});

					if (response.code === '0000' && response.data) {
						const data = response.data;
						this.currentQRCodeId = data.qrCodeId;
						this.qrCodeBase64 = data.qrCodeBase64;
						this.qrCodeExpireTime = data.expireTime;

						// 检查二维码状态
						this.isQRExpired = data.qrCodeState === '0';
					} else {
						throw new Error(response.msg || '获取二维码信息失败');
					}
				} catch (error) {
					console.error('获取二维码信息失败:', error);
					// 如果获取失败，显示默认状态
					this.qrCodeBase64 = '';
					this.isQRExpired = true;
					uni.showToast({
						title: '获取二维码失败',
						icon: 'none'
					});
				}
			},

			// 退出确认弹窗处理
			handleExitConfirmCancel() {
				this.showExitConfirmModal = false;
			},

			async handleExitConfirmConfirm(password) {
				console.log('触发退出确认，密码:', password);
				this.exitConfirmLoading = true;
				
				try {
					// 调用HTTP接口验证密码
					const result = await verifyPassword({
						workOrderId: this.workOrderId,
						passwordLast4: password
					});
					
					if (result.code === '0000' && result.data && result.data.checkResult) {
						// 密码正确
						if (this.wsConnected) {
							// WebSocket已连接，发送主动退出消息
							this.sendActiveExit();
							// 设置超时处理
							this.setExitTimeout();
						} else {
							// WebSocket未连接，直接退出
							this.exitConfirmLoading = false;
							this.showExitConfirmModal = false;
							uni.showToast({
								title: '退出成功',
								icon: 'success'
							});
							setTimeout(() => {
								this.cleanupWebSocket();
								uni.navigateBack();
							}, 1000);
						}
					} else {
						// 密码错误
						this.exitConfirmLoading = false;
						if (this.$refs.exitConfirmModal) {
							this.$refs.exitConfirmModal.setError('密码验证失败，请重新输入');
						}
					}
				} catch (error) {
					this.exitConfirmLoading = false;
					console.error('密码验证失败:', error);
					if (this.$refs.exitConfirmModal) {
						this.$refs.exitConfirmModal.setError('网络错误，请重试');
					}
				}
			},

			// 处理要求退出
			handleRequireExit() {
				uni.showModal({
					title: '提示',
					content: '发现有其他设备已在执行该工单，请立刻退出。',
					showCancel: false,
					confirmText: '确定',
					success: () => {
						this.cleanupWebSocket();
						uni.navigateBack();
					}
				});
			},

			// 处理退出成功
			handleExitSuccess() {
				this.clearExitTimeout();
				this.showExitConfirmModal = false;
				this.exitConfirmLoading = false;
				uni.showToast({
					title: '退出成功',
					icon: 'success'
				});
				setTimeout(() => {
					this.cleanupWebSocket();
					uni.navigateBack();
				}, 1000);
			},

			// 设置退出超时处理
			setExitTimeout() {
				this.clearExitTimeout();
				this.exitTimeout = setTimeout(() => {
					this.exitConfirmLoading = false;
					uni.showToast({
						title: '请求超时，请重试',
						icon: 'none'
					});
				}, 5000); // 5秒超时
			},

			// 清除退出超时
			clearExitTimeout() {
				if (this.exitTimeout) {
					clearTimeout(this.exitTimeout);
					this.exitTimeout = null;
				}
			},

			// 清理WebSocket连接
			cleanupWebSocket() {
				if (this.workOrderWS) {
					console.log('清理WebSocket连接');
					this.workOrderWS.closeSocket();
					this.workOrderWS = null;
				}
				this.wsConnected = false;
				this.wsConnecting = false;
				this.clearExitTimeout();
			},

			exitAssessment() {
				// 只有当工单状态为 TODO 或 DOING 时才需要密码验证
				if (this.workOrderState !== 'TODO' && this.workOrderState !== 'DOING') {
					// 其他状态（DONE、CANCELED等）直接退出
					uni.navigateBack();
					return;
				}

				// 对于 TODO/DOING 状态的工单，优先显示密码验证弹窗
				// 只有在WebSocket初始化失败且不在连接中的情况下才使用确认方式
				if (!this.wsConnected && !this.wsConnecting && !this.workOrderWS) {
					// WebSocket完全未初始化或初始化失败，使用确认退出方式
					uni.showModal({
						title: '确认退出',
						content: '确定要退出测评吗？',
						success: (res) => {
							if (res.confirm) {
								uni.navigateBack();
							}
						}
					});
					return;
				}

				// 其他情况（WebSocket已连接、正在连接中、或已初始化但连接失败）都显示密码验证弹窗
				this.showExitConfirmModal = true;
			},
			async viewDetail(item) {
				console.log('查看详情:', item);
				
				// 只有已完成的测评才能获取报告详情
				if (item.state !== '1') {
					uni.showToast({
						title: '测评未完成，无法获取报告',
						icon: 'none'
					});
					return;
				}
				
				try {
					uni.showLoading({
						title: '获取报告中...'
					});
					
					// 调用接口获取报告详情
					const response = await exportProjectEvaluationPdf({
						projectId: item.projectId,
						evaluatId: item.projectId, // 根据实际情况可能需要调整
						traineeId: this.traineeId
					});
					
					uni.hideLoading();
					
					if (response.code === '0000' && response.data) {
						const { doc, pdf } = response.data;
						
						// 检查是否有文档
						if (!doc && !pdf) {
							uni.showToast({
								title: '暂无报告文档',
								icon: 'none'
							});
							return;
						}
						
						// 如果只有一种格式，直接打开
						if ((doc && !pdf) || (!doc && pdf)) {
							const url = doc || pdf;
							this.openDocumentWithExternalApp(url);
						} else {
							// 如果有多种格式，显示选择弹窗
							this.currentReportData = { doc, pdf };
							this.showReportModal = true;
						}
					} else {
						uni.showToast({
							title: response.msg || '获取报告失败',
							icon: 'none'
						});
					}
				} catch (error) {
					uni.hideLoading();
					console.error('获取报告详情失败:', error);
					uni.showToast({
						title: '获取报告失败，请重试',
						icon: 'none'
					});
				}
			},
			retakeAssessment(item) {
				console.log('重新测评:', item)

				// 根据 quizType 字段跳转到不同的页面，QUEST 保持原有逻辑
				const quizType = item.quizType;

				if (quizType === 'QUEST' || !quizType) {
					// QUEST 类型或未定义时，保持原有逻辑 - 跳转至 SNAP-IV 量表介绍页
					uni.navigateTo({
						url: `/pages/scale/snap-iv-introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${this.workOrderId}&traineeId=${this.traineeId}`
					})
				} else {
					// 其他类型根据 quizType 跳转
					switch (quizType) {
						case 'CPT-STD':
							// IVA-CPT 标准版 - 跳转至 IVA-CPT 介绍页
							uni.navigateTo({
								url: `/pages/ivacpt/introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${this.workOrderId}&traineeId=${this.traineeId}`
							})
							break;
						case 'CPT-MINI':
							// IVA-CPT MINI版 - 跳转至 IVA-CPT MINI 介绍页
							uni.navigateTo({
								url: `/pages/ivacpt/introduce?type=mini&projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${this.workOrderId}&traineeId=${this.traineeId}`
							})
							break;
						case 'PPVT':
							// PPVT 测评 - 跳转至 PPVT 介绍页
							uni.navigateTo({
								url: `/pages/introduce/index?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${this.workOrderId}&traineeId=${this.traineeId}`
							})
							break;
						default:
							// 未知类型，使用原有逻辑
							console.warn('未知的 quizType:', quizType);
							uni.navigateTo({
								url: `/pages/scale/snap-iv-introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${this.workOrderId}&traineeId=${this.traineeId}`
							})
							break;
					}
				}
			},
			startAssessment(item) {
				console.log('开始测评:', item)

				// 根据 quizType 字段跳转到不同的页面，QUEST 保持原有逻辑
				const quizType = item.quizType;

				if (quizType === 'QUEST' || !quizType) {
					// QUEST 类型或未定义时，保持原有逻辑 - 跳转至 SNAP-IV 量表介绍页
					uni.navigateTo({
						url: `/pages/scale/snap-iv-introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${this.workOrderId}&traineeId=${this.traineeId}`
					})
				} else {
					// 其他类型根据 quizType 跳转
					switch (quizType) {
						case 'CPT-STD':
							// IVA-CPT 标准版 - 跳转至 IVA-CPT 介绍页
							uni.navigateTo({
								url: `/pages/ivacpt/introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${this.workOrderId}&traineeId=${this.traineeId}`
							})
							break;
						case 'CPT-MINI':
							// IVA-CPT MINI版 - 跳转至 IVA-CPT MINI 介绍页
							uni.navigateTo({
								url: `/pages/ivacpt/introduce?type=mini&projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${this.workOrderId}&traineeId=${this.traineeId}`
							})
							break;
						case 'PPVT':
							// PPVT 测评 - 跳转至 PPVT 介绍页
							uni.navigateTo({
								url: `/pages/introduce/index?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${this.workOrderId}&traineeId=${this.traineeId}`
							})
							break;
						default:
							// 未知类型，使用原有逻辑
							console.warn('未知的 quizType:', quizType);
							uni.navigateTo({
								url: `/pages/scale/snap-iv-introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${this.workOrderId}&traineeId=${this.traineeId}`
							})
							break;
					}
				}
			},
			
			// 关闭报告文档弹窗
			closeReportModal() {
				this.showReportModal = false;
				this.currentReportData = {};
			},
			
			// 打开报告文档
			openReportDocument({ type, url }) {
				console.log('打开文档:', type, url);
				this.openDocumentWithExternalApp(url);
			},
			
			// 使用外部程序打开文档
			openDocumentWithExternalApp(url) {
				console.log('使用外部程序打开文档:', url);
				
				// #ifdef APP-PLUS
				// App环境下使用plus.runtime.openURL
				plus.runtime.openURL(url, (error) => {
					console.error('打开文档失败:', error);
					uni.showToast({
						title: '打开文档失败',
						icon: 'none'
					});
				});
				// #endif
				
				// #ifdef H5
				// H5环境下直接打开新窗口
				window.open(url, '_blank');
				// #endif
				
				// #ifdef MP
				// 小程序环境下复制链接到剪贴板
				uni.setClipboardData({
					data: url,
					success: () => {
						uni.showToast({
							title: '链接已复制到剪贴板',
							icon: 'success'
						});
					},
					fail: () => {
						uni.showToast({
							title: '复制链接失败',
							icon: 'none'
						});
					}
				});
				// #endif
			}
		}
	}
</script>

<style lang="scss" scoped>
	.assessment-detail-page {
		width: 100vw;
		height: 100vh;
		background: #F6F6F6;
		display: flex;
		flex-direction: column;
		overflow: hidden;
	}
	
	/* 状态栏 */
	.status-bar {
		width: 100%;
		height: 33px;
		background: #FFFFFF;
		flex-shrink: 0;
	}
	
	/* 顶部功能区 */
	.header {
		position: relative;
		width: 100%;
		height: 92px;
		flex-shrink: 0;
		
		.header-bg {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			background: #287FFF;
		}
		
		.patient-info {
			position: absolute;
			left: 50%;
			top: 58px;
			transform: translateX(-50%);
			font-family: 'Alibaba PuHuiTi';
			font-size: 17px;
			color: #FFFFFF;
			font-weight: 400;
		}
		
		.refresh-btn {
			position: absolute;
			right: 78px;
			top: 50%;
			transform: translateY(-50%);
			width: 22px;
			height: 22px;
			
			.refresh-icon {
				width: 100%;
				height: 100%;
				background: url('/static/refresh-icon.png') no-repeat center;
				background-size: contain;
			}
		}
		
		.refresh-text {
			position: absolute;
			right: 22px;
			top: 50%;
			transform: translateY(-50%);
			font-family: 'Alibaba PuHuiTi';
			font-size: 22px;
			color: #FFFFFF;
			font-weight: 400;
		}
		
		.instruction {
			position: absolute;
			left: 50%;
			top: 17px;
			transform: translateX(-50%);
			font-family: 'Alibaba PuHuiTi';
			font-size: 28px;
			color: #FFFFFF;
			font-weight: 400;
		}
		
		.exit-btn {
			position: absolute;
			left: 22px;
			top: 24px;
			width: 154px;
			height: 44px;
			background: #1D69D8;
			border-radius: 11px;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 11px;
			
			.exit-icon {
				width: 22px;
				height: 22px;
				background: url('/static/logout-icon.png') no-repeat center;
				background-size: contain;
			}
			
			.exit-text {
				font-family: 'Alibaba PuHuiTi';
				font-size: 22px;
				color: #FFFFFF;
				font-weight: 400;
			}
		}
	}
	
	/* 选项卡组合 */
	.tab-container {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 0 28px;
		margin-top: 27px;
		background: #FFFFFF;
		border-radius: 11px 11px 0 0;
		border-bottom: 1px solid #EEEEEE;
		gap: 11px;
		padding: 8px;
		width: calc(100% - 56px);
		
		.tab-group {
			display: flex;
			gap: 11px;
		}
		
		.tab-item {
			display: flex;
			align-items: center;
			padding: 14px 17px;
			border-radius: 11px 11px 0 0;
			background: #F3F8FF;
			width: 180px;
			justify-content: center;
			
			&.active {
				background: #287FFF;
				
				.tab-text {
					color: #FFFFFF;
				}
			}
			
			.tab-text {
				font-family: 'Alibaba PuHuiTi';
				font-size: 19px;
				color: #287FFF;
				font-weight: 400;
				line-height: 1em;
			}
		}
		
		/* 二维码按钮区域 */
		.qr-code-section {
			display: flex;
			align-items: center;
		}
		
		.qr-code-btn {
			display: flex;
			align-items: center;
			gap: 3px;
			cursor: pointer;
			padding: 8px 12px;
			border-radius: 6px;
			transition: background-color 0.2s;
			
			&:hover {
				background: #F3F8FF;
			}
			
			.qr-icon {
				width: 28px;
				height: 28px;
			}
			
			.qr-text {
				font-family: 'Alibaba PuHuiTi';
				font-size: 19px;
				color: #287FFF;
				font-weight: 400;
			}
		}
	}
	
	/* 表格容器 */
	.table-container {
		margin: 0 28px;
		margin-top: -1px;
		background: #FFFFFF;
		border-radius: 0 0 11px 11px;
		flex: 1;
		display: flex;
		flex-direction: column;
		min-height: 0;
		overflow: hidden;
	}
	
	.table-header {
		display: flex;
		background: #EEEEEE;
		flex-shrink: 0;
		
		.header-cell {
			font-family: 'Alibaba PuHuiTi';
			font-weight: 400;
			font-size: 17px;
			color: #333333;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 11px 8px;
			border-bottom: 1px solid #EEEEEE;
			border-right: 1px solid #EEEEEE;
			
			&:last-child {
				border-right: none;
			}
		}
	}
	
	.table-body {
		flex: 1;
		overflow-y: auto;
		min-height: 0;
		
		.table-row {
			display: flex;
			
			&:hover {
				background: #F8F9FA;
			}
		}
	}
	
	.table-cell {
		font-family: 'Alibaba PuHuiTi';
		font-size: 17px;
		color: #333333;
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 6px 8px;
		border-bottom: 1px solid #EEEEEE;
		border-right: 1px solid #EEEEEE;
		min-height: 61px;
		box-sizing: border-box;
		
		&:last-child {
			border-right: none;
		}
	}
	
	/* 列宽设置（按比例占满整行） */
	.col-index {
		flex: 0 0 9%;
	}
	
	.col-project {
		flex: 0 0 54%;
		justify-content: flex-start;
		
		.project-info {
			display: flex;
			align-items: center;
			gap: 11px;
			width: 100%;
			
			.project-name {
				font-size: 17px;
				color: #333333;
			}
			
			.project-tag {
				padding: 6px 11px;
				border-radius: 6px;
				
				&.blue {
					background: #EBF2FF;
					
					.tag-text {
						color: #287FFF;
					}
				}
				
				&.orange {
					background: #FFEFD2;
					
					.tag-text {
						color: #FFB130;
					}
				}
				
				&.green {
					background: #F6FFED;
					
					.tag-text {
						color: #52C41A;
					}
				}
				
				.tag-text {
					font-family: 'Alibaba PuHuiTi';
					font-size: 17px;
					font-weight: 400;
				}
			}
		}
	}
	
	.col-status {
		flex: 0 0 15%;
		justify-content: flex-start;
		padding-left: 20px;
	}
	
	.col-score {
		flex: 0 0 9%;
	}
	
	.col-action {
		flex: 0 0 13%;
		justify-content: flex-start;
		padding-left: 20px;
	}
	
	.status-tag {
		padding: 6px 11px;
		border-radius: 6px;
		
		/* 参考index.vue中的状态样式 */
		&.TODO { /* 待测评 */
			background: #FFE7E7;
			
			.status-text {
				color: #FF4D50;
			}
		}
		
		&.DOING { /* 执行中 */
			background: #D7FFEF;
			
			.status-text {
				color: #20DF92;
			}
		}
		
		&.DONE { /* 已完成 */
			background: #EBF2FF;
			
			.status-text {
				color: #287FFF;
			}
		}
		
		&.CANCELED { /* 已撤销 */
			background: #FFEFD2;
			
			.status-text {
				color: #FFB130;
			}
		}
		
		.status-text {
			font-family: 'Alibaba PuHuiTi';
			font-size: 17px;
			font-weight: 400;
		}
	}
	
	.action-btns {
		display: flex;
		align-items: center;
		gap: 6px;
		
		.action-btn {
			font-family: 'Alibaba PuHuiTi';
			font-size: 17px;
			color: #287FFF;
			cursor: pointer;
			
			&:hover {
				text-decoration: underline;
			}
		}
		
		.btn-divider {
			width: 1px;
			height: 11px;
			background: #EEEEEE;
		}
	}
	
	/* 二维码弹窗样式 */
	.qr-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		display: flex;
		justify-content: center;
		align-items: center;
		z-index: 9999;
	}
	
	.qr-modal-content {
		width: 500px;
		background: #FFFFFF;
		border-radius: 22px;
		padding: 28px;
		display: flex;
		flex-direction: column;
		gap: 39px;
		box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
	}
	
	.qr-content-wrapper {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100%;
	}
	
	.qr-image-section {
		display: flex;
		justify-content: center;
		margin-bottom: 14px;
	}
	
	.qr-code-image {
		width: 278px;
		height: 278px;
		border-radius: 8px;
	}
	
	.qr-expired-placeholder {
		width: 278px;
		height: 278px;
		border-radius: 8px;
		border: 2px dashed #CCCCCC;
		background: #F8F8F8;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		gap: 16px;
		
		.expired-icon {
			width: 48px;
			height: 48px;
			border-radius: 50%;
			background: #FF4D50;
			color: #FFFFFF;
			font-family: 'Alibaba PuHuiTi';
			font-size: 32px;
			font-weight: 700;
			display: flex;
			align-items: center;
			justify-content: center;
			line-height: 1;
		}
		
		.expired-text {
			font-family: 'Alibaba PuHuiTi';
			font-size: 18px;
			color: #999999;
			font-weight: 400;
		}
	}
	
	.qr-text-section {
		display: flex;
		flex-direction: column;
		align-items: center;
		width: 100%;
		gap: 8px;
	}
	
	.qr-main-text {
		font-family: 'Alibaba PuHuiTi';
		font-size: 22px;
		font-weight: 400;
		color: #666666;
		line-height: 1.5;
		text-align: center;
		width: 100%;
	}
	
	.qr-sub-text {
		font-family: 'Alibaba PuHuiTi';
		font-size: 22px;
		font-weight: 400;
		color: #287FFF;
		line-height: 1.5;
		text-align: center;
		width: 100%;
		
		&.expired {
			color: #FF4D50;
		}
	}
	
	.qr-button-section {
		display: flex;
		justify-content: space-between;
		gap: 26px;
		width: 100%;
	}
	
	.qr-button {
		display: flex;
		justify-content: center;
		align-items: center;
		padding: 17px 63px;
		border-radius: 11px;
		cursor: pointer;
		transition: all 0.2s;
		min-width: 180px;
		
		.button-text {
			font-family: 'Alibaba PuHuiTi';
			font-size: 22px;
			font-weight: 400;
			line-height: 1.25;
		}
		
		&.close-btn {
			background: #FFFFFF;
			border: 1px solid #287FFF;
			
			.button-text {
				color: #287FFF;
			}
			
			&:hover {
				background: #F3F8FF;
			}
		}
		
		&.regenerate-btn {
			background: #287FFF;
			
			.button-text {
				color: #FFFFFF;
			}
			
			&:hover {
				background: #1E6FE6;
			}
		}
	}
	
	/* 报告选项浮窗样式 */
	.report-modal {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 8888;
	}
	
	.report-modal-content {
		position: absolute;
		background: #FFFFFF;
		border-radius: 8px;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		border: 1px solid #E8E8E8;
		overflow: hidden;
		min-width: 120px;
	}
	
	.report-option-item {
		display: flex;
		align-items: center;
		padding: 12px 16px;
		cursor: pointer;
		transition: background-color 0.2s;
		
		&:hover {
			background: #F3F8FF;
		}
		
		&:not(:last-child) {
			border-bottom: 1px solid #F0F0F0;
		}
		
		.option-text {
			font-family: 'Alibaba PuHuiTi';
			font-size: 16px;
			color: #333333;
			font-weight: 400;
		}
	}
</style>