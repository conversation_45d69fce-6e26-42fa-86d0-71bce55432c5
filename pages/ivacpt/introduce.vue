<template>
	<IvaLineVue></IvaLineVue>
	<view class="introduce" @click="go">
		<view class="introduce-click ">
			<image class="introduce-click-img" src="/static/ivacpt/iva-clcik-right.gif" mode="widthFix"></image>
			点击屏幕任意位置，进入测评<view class="">
				测评过程中，<text style="color: #FFAB01;">手指抬离屏幕1cm</text>
			</view>
		</view>
		<view class="introduce-title">
			视听整合持续性操作测验
		</view>
		<view class="introduce-text">
			{{props.type==='mini'?'IVA-CPT（MINI版）':'（IVA-CPT）2023'}}
		</view>
		<view class="introduce-box center">
			<view class="introduce-box-other center">
				<view class="introduce-box-other-other1">

				</view>
			</view>
		</view>
		<view class="introduce-tips">
			我们现在进行测验前的练习，注意看、听和点击屏幕，<br />
			测试过程中，可从屏幕侧边滑动，中止测试
		</view>
		<!-- 	<view class="introduce-btntwo">
			<view class="introduce-btntwo-value center" style="color:#287FFF ;" @click="open">
				佩戴BCI
			</view>
			<view class="introduce-btntwo-value center" style="color:#F4A300 ;" @click="go">
				未戴BCI
			</view>
		</view> -->
	</view>
	<!-- 	<uni-popup ref="popup" type="center" :is-mask-click="true">
		<PopBoxVue :text="{value:''}" title="当前设备未连接" :btn="{center:'前往设备管理'}" @click="goDevice"></PopBoxVue>
	</uni-popup> -->
</template>

<script setup>
	import IvaLineVue from '../../components/IvaLine.vue';
	import PopBoxVue from '../../components/PopBox.vue';
	import {
		onMounted,
		onUnmounted,
		ref
	} from 'vue';
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		navigateTo
	} from '../../common/uniTool';
	import {
		useSocketStore
	} from '@/stores/socket';
	// import {
	// 	useHelper
	// } from '@/stores/helper';
	// const helper = useHelper(); //设备仓库
	const props = defineProps(['type', 'projectId', 'projectName', 'workOrderId', 'traineeId'])
	const socket = useSocketStore(); //websocket仓库
	const popup = ref(null)
	const innerAudioRef = ref(null)
	onMounted(() => {
		innerAudioRef.value = uni.createInnerAudioContext()
		innerAudioRef.value.src = '/static/audio/ivacpt/iva-introduce-sta.mp3'
		innerAudioRef.value.play()
	})
	onHide(() => {
		// popup.value.close()
		if (innerAudioRef.value) {
			innerAudioRef.value.destroy()
			innerAudioRef.value = null
		}
	})
	onUnmounted(() => {
		if (innerAudioRef.value) {
			innerAudioRef.value.destroy()
			innerAudioRef.value = null
		}
	})
	onShow(() => {
		// #ifdef APP-PLUS
		if (socket.socketTask) {
			console.log('断开连接');
			socket.socketTask.closeSocket()
		}
		// #endif
	})
	const open = () => {
		const baseParams = `projectId=${props.projectId || ''}&projectName=${encodeURIComponent(props.projectName || '')}&workOrderId=${props.workOrderId || ''}&traineeId=${props.traineeId || ''}`
		
		// if (!helper.address) {
		// 	popup.value.open()
		// } else {
		navigateTo(`/pages/ivacpt/standard?${baseParams}`)
		// }
	}
	const go = () => {
		const baseParams = `projectId=${props.projectId || ''}&projectName=${encodeURIComponent(props.projectName || '')}&workOrderId=${props.workOrderId || ''}&traineeId=${props.traineeId || ''}`
		
		if (props.type === 'mini') {
			navigateTo(`/pages/ivacpt/index?${baseParams}`)
		} else {
			navigateTo(`/pages/ivacpt/standard?${baseParams}`)
		}
	}
	const goDevice = () => {
		navigateTo('/pages/device/index')
	}
</script>

<style lang="scss">
	.introduce {
		width: 100vw;
		height: 100vh;
		display: flex;
		flex-direction: column;
		align-items: center;
		position: fixed;
		z-index: 2;

		&-click {
			position: absolute;
			right: 4%;
			top: 40%;
			transform: translateZ(-50%);
			font-family: SourceHanSansCN, SourceHanSansCN;
			font-weight: bold;
			font-size: 40rpx;
			color: #FFFFFF;
			display: flex;
			flex-direction: column;
			align-items: center;

			&-img {
				width: 400rpx;
			}
		}

		&-btntwo {
			width: 100%;
			display: flex;
			align-items: center;
			justify-content: space-around;

			&-value {
				width: 330rpx;
				height: 116rpx;
				background: #FFFFFF;
				border-radius: 24rpx;
				font-size: 48rpx;
				font-family: SourceHanSansCN-Medium, SourceHanSansCN;
				font-weight: bold;
			}
		}

		&-title {
			font-family: SourceHanSansCN, SourceHanSansCN;
			font-weight: bold;
			font-size: 75rpx;
			color: #FFFFFF;
			margin-top: 90rpx;
			margin-bottom: 32rpx;
		}

		&-text {
			font-family: SourceHanSansCN, SourceHanSansCN;
			font-weight: bold;
			font-size: 60rpx;
			color: #FFFFFF;
		}

		&-box {
			width: 619rpx;
			height: 788rpx;
			border-radius: 32rpx;
			border: 4rpx solid #FFDF9E;
			margin-top: 26rpx;
			margin-bottom: 55rpx;
			flex-shrink: 0;

			&-other {
				width: 581rpx;
				height: 750rpx;
				border: 4rpx solid #FFDF9E;
				border-radius: 16rpx;

				&-other1 {
					width: 544rpx;
					height: 713rpx;
					border: 4rpx solid #FFDF9E;
					border-radius: 10rpx;
				}
			}
		}

		&-tips {
			font-family: SourceHanSansCN, SourceHanSansCN;
			font-weight: bold;
			font-size: 45rpx;
			color: #FFAB01;
			line-height: 60rpx;
			text-align: center;
			margin-bottom: 90rpx;
		}

		&-btn {
			width: 480rpx;
			height: 116rpx;
			background: #FFFFFF;
			border-radius: 108rpx;
			font-size: 48rpx;
			font-family: SourceHanSansCN-Medium, SourceHanSansCN;
			font-weight: bold;
			color: #111111;
		}
	}
</style>