<template>
	<clickEffect :isClick="state.isClick"></clickEffect>
	<IvaLineVue></IvaLineVue>
	<view class="iva" @click="click">
		<StepsFour type="mini" :options="state.stepList" :active="state.active"></StepsFour>
		<view class="iva-content">
			<view class="iva-content-box" v-if="state.active===1">
				<view class="iva-content-box-title">
					<view class="iva-content-box-title-icon center iconfont">&#xe6b3;</view>
					{{rules[state.teach].text}}
				</view>
				<view class="iva-content-box-value center">
					<view class="iva-content-box-value-other center">
						<view class="iva-content-box-value-other-other1 center">
							{{state.showNum1?'1':state.showNum2?'2':''}}
						</view>
					</view>
				</view>
				<view class="iva-content-box-tips">
					{{rules[state.teach].rule}}
					<image class="iva-content-box-tips-five" v-if="state.showFive" :src="rules[state.teach].gif" mode="widthFix"></image>
				</view>
			</view>
			<view v-if="state.active===2" class="iva-content-box">
				<view class="iva-content-box-rule" v-if="state.tip">
					<image src="/static/ivacpt/iva-content-img1.png" class="iva-content-box-rule-img" mode="widthFix"></image>
					<view class="iva-content-box-rule-text">
						20个试次
					</view>
					<view class="iva-content-box-rule-btn center" @click.stop="testStart">
						开始练习
					</view>
				</view>
				<view class="iva-content-box-tril" v-else>
					{{state.testRound}}/20
				</view>
				<view class="iva-content-box-value center" v-show="!state.tip">
					<view class="iva-content-box-value-other center">
						<view class="iva-content-box-value-other-other1 center">
							{{state.showNum1?'1':state.showNum2?'2':''}}
						</view>
					</view>
				</view>
			</view>
			<view v-if="state.active===3" class="iva-content-box">
				<view class="iva-content-box-rule" v-if="state.tip">
					<image src="/static/ivacpt/iva-content-img2.png" class="iva-content-box-rule-img" mode="widthFix"></image>
					<view class="iva-content-box-rule-text">
						听到或看到<text style="font-weight: bold;">“ 1 ”</text>，请单击屏幕<br /> 听到或看到<text style="font-weight: bold;">“ 2 ”</text>，请不要点击
					</view>
					<view class="iva-content-box-rule-btn center" @click.stop="gameStart">
						开始测评
					</view>
				</view>
				<view class="iva-content-box-value center" v-show="!state.tip">
					<view class="iva-content-box-value-other center">
						<view class="iva-content-box-value-other-other1 center">
							{{state.showNum1?'1':state.showNum2?'2':''}}
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>
	<uni-popup ref="popup" type="center" :is-mask-click="false">
		<PopBoxVue v-if="state.active===2" :text="{value:'由于你误操作过多，可能还不明白规则','other':'我们回到规则讲解页面'}" :btn="{center:'重新了解学习规则'}" @click="back"></PopBoxVue>
		<PopBoxVue v-else title="测评结束" :text="{value:''}" :btn="{center:'回到首页'}" @click="go"></PopBoxVue>
	</uni-popup>
	<IvaDropoutReminderVue :eceuId="eceuId" :isStartCollect="isStartCollect"></IvaDropoutReminderVue>
</template>

<script setup>
	import IvaLineVue from '@/components/IvaLine.vue';
	import StepsFour from '@/components/StepsFour.vue';
	import IvaDropoutReminderVue from '../../components/IvaDropoutReminder.vue';
	import {
		onMounted,
		onUnmounted,
		reactive,
		ref,
		watch
	} from "vue";
	import {
		getStorageSync,
		redirectTo
	} from '@/common/uniTool';
	import PopBoxVue from '@/components/PopBox.vue';
	import {
		getIvacptData,
		subIvacptMiniData
	} from '@/service/ivacpt';
	import {
		useUserStore
	} from '../../stores/user';
	import {
		reportUrl,
		wsUrl
	} from '@/common/global';
	import clickEffect from '@/components/pc-clickEffect/pc-clickEffect.vue'
	import getIVAData from './ivaData';

	const props = defineProps(['projectId', 'projectName', 'workOrderId', 'traineeId'])
	const userStore = useUserStore()
	const popup = ref(null)
	const showDrop = ref(false)
	const innerAudioContextRef = ref(null) //音频
	const timeRef = ref(null) //定时器
	const eventInfos = ref([]) //事件信息
	const eceuId = ref('') //时间戳id
	const isStartCollect = ref(false) //是否开始脑电采集
	const rules = [{
		text: '你会在屏幕上看到闪的数字1',
		rule: '看到1，请尽快单击屏幕',
		boxValue: '1',
		gif: '/static/ivacpt/growth-path-five.gif'
	}, {
		text: '你会听到语音播报数字1',
		rule: '听到1，请尽快单击屏幕',
		boxValue: '',
		gif: '/static/ivacpt/growth-path-five.gif'
	}, {
		text: '你会在屏幕上看到闪的数字2',
		rule: '看到2，请不要点击',
		boxValue: '2',
		gif: '/static/ivacpt/iva-err.png'
	}, {
		text: '你会听到语音播报数字2',
		rule: '听到2，请不要点击',
		boxValue: '',
		gif: '/static/ivacpt/iva-err.png'
	}]
	const state = reactive({
		stepList: [{
			title: '规则讲解'
		}, {
			title: '热身练习'
		}, {
			title: '正式阶段'
		}],
		active: 1,
		step: 0, //音频步骤
		isMp3End: false,
		teach: 0, //音频步骤
		showNum1: false,
		showNum2: false,
		tip: true, //提醒过渡开始阶段
		testRound: 0, //测试轮次
		isClick: false, //是否开始添加数据
		showFive: false,
		clickNum: 0, //点击次数
		clickErr: 0, //连续错误次数
		round: 0, //正式环节轮次
		ivaData: null
	})

	onMounted(() => {

		getData('0')
		innerAudioContextRef.value = uni.createInnerAudioContext()
		innerAudioContextRef.value.src = '/static/audio/ivacpt/tips-1-1.mp3'
		innerAudioContextRef.value.play()
		innerAudioContextRef.value.onEnded(() => {
			if (state.step === 1) {
				state.showFive = true
				setTimeout(() => {
					innerAudioContextRef.value.destroy()
					innerAudioContextRef.value = null
					state.showFive = false
					state.isMp3End = true
					state.teach = 1
				}, 1000)
			}
			if (state.step === 0) {
				showNum('1')
				innerAudioContextRef.value.src = '/static/audio/ivacpt/tips-1-2.mp3'
				innerAudioContextRef.value.play()
				state.step = 1
			}
		})

	})

	const getData = (num) => {
		getIvacptData({
			outpatientId: userStore.outpatientId,
			type: num,
			evaluatId: num === '0' ? null : state.ivaData.evaluatId,
			version: 'MINI'
		}).then(res => {
			state.ivaData = res.data
			eceuId.value = res.data.eceuId
		})
	}
	onUnmounted(() => {
		innerAudioContextRef.value && innerAudioContextRef.value.destroy()
		timeRef.value && clearInterval(timeRef.value)
	})
	watch(() => state.clickErr, (clickErr) => {
		if (clickErr >= 3) { //连续错3个回到规则讲解
			clearInterval(timeRef.value)
			popup.value.open()
		}
	})
	watch(() => state.active, (active) => {
		if (active === 3) { //测试结束提交数据进入正式阶段
			console.log('测试结束提交数据进入正式阶段');
			postData('0')
			state.tip = true

			eventInfos.value = []
		}
	})
	watch([() => state.step, () => state.isMp3End], ([step, isMp3End]) => {
		if (step === 1 && isMp3End) {
			innerAudioContextRef.value = uni.createInnerAudioContext()
			innerAudioContextRef.value.src = '/static/audio/ivacpt/tips-2-1.mp3'
			innerAudioContextRef.value.play()
			innerAudioContextRef.value.onEnded(() => {
				state.showFive = true
				setTimeout(() => {
					if (state.step === 1) {
						innerAudioContextRef.value.src = '/static/audio/ivacpt/tips-3-1.mp3'
						innerAudioContextRef.value.play()
						state.teach = 2
						state.step = 2
					}
				}, 1000)
			})
		} else if (step === 2) {
			innerAudioContextRef.value.onEnded(() => {
				showNum('2')
				innerAudioContextRef.value.destroy()
				innerAudioContextRef.value = null
				state.step = 3

			})
		} else if (step === 3) {
			innerAudioContextRef.value = uni.createInnerAudioContext()
			innerAudioContextRef.value.src = '/static/audio/ivacpt/tips-3-2.mp3'
			innerAudioContextRef.value.play()
			innerAudioContextRef.value.onEnded(() => {
				innerAudioContextRef.value.src = '/static/audio/ivacpt/tips-4-1.mp3'
				innerAudioContextRef.value.play()
				state.teach = 3
				state.step = 4
			})
		} else if (step === 4) {
			innerAudioContextRef.value.onEnded(() => {
				innerAudioContextRef.value.destroy()
				innerAudioContextRef.value = null
				state.active = 2
			})
		}

	})
	const getType = (round, level) => {
		const data = getIVAData('mini', level)
		let num = data['numList'][round]
		let type = data['signalList'][round]
		let modType = data['modTypelList'][round]
		let signal = data['signalList'][round]
		if (type === 'A') {
			playNum(num, modType, signal)
		}
		if (type === "V") {
			showNum(num, modType, signal)
		}
	}
	const showNum = (num, modType, signal) => {
		if (state.isClick) {
			eventInfos.value.push({
				beginDate: new Date().getTime(),
				clickDate: [],
				modType,
				num,
				signal
			})
		}
		if (num === '1') {
			state.showNum1 = true
			setTimeout(() => {
				state.showNum1 = false
			}, 267)
		} else {
			state.showNum2 = true
			setTimeout(() => {
				state.showNum2 = false
			}, 267)
		}

	}
	const playNum = (num, modType, signal) => {
		if (state.isClick) {
			eventInfos.value.push({
				beginDate: new Date().getTime(),
				clickDate: [],
				modType,
				num,
				signal
			})
		}
		if (num === '1') {
			innerAudioContextRef.value.src = '/static/audio/ivacpt/iva-1.mp3'
			innerAudioContextRef.value.play()
		} else {
			innerAudioContextRef.value.src = '/static/audio/ivacpt/iva-2.mp3'
			innerAudioContextRef.value.play()
		}
	}
	const gameStart = () => {
		isStartCollect.value = true
		state.tip = false
		timeRef.value = setInterval(() => {
			state.round++ //轮次加一
			state.isClick = true
			getType(state.round - 1, '1')
			if (state.round === 300) { //300轮次后正式环节结束打开弹窗
				clearInterval(timeRef.value)
				setTimeout(() => {
					state.isClick = false
					postData('1')
					popup.value.open()
				}, 1500)
			}
		}, 1500)
	}
	const testStart = () => {
		isStartCollect.value = true
		innerAudioContextRef.value = uni.createInnerAudioContext()
		state.tip = false
		timeRef.value = setInterval(() => {
			if (state.clickNum === 0 || state.clickNum > 1) { //不点击事件错误事件和超过一个点击错误事件
				state.clickErr++
			} else { //当点击正确次数1个时候非连续重置
				state.clickErr = 0
			}
			state.testRound++ //轮次加一
			state.isClick = true
			state.clickNum = 0 //新的一轮重置点击次数
			getType(state.testRound - 1, '0')
			if (state.testRound === 20) { //20测试环节结束进入正式环节
				clearInterval(timeRef.value)
				setTimeout(() => {
					state.isClick = false
					state.active = 3
				}, 1500)
			}
		}, 1500)
	}
	const postData = (num) => {
		isStartCollect.value = false
		subIvacptMiniData({
			evaluatId: state.ivaData.evaluatId,
			outpatientId: userStore.outpatientId,
			type: num,
			eventInfos: eventInfos.value,
			projectId: props.projectId,
			workorderId: props.workOrderId
		}).then(res => {
			if (num === '0') { //先提交上一个id后get拿到唯一id
				getData('1')
			}
		})
	}
	const click = (e) => {
		if (state.isClick) {
			if (state.active === 2) {
				eventInfos.value[state.testRound - 1]['clickDate'].push(new Date().getTime())
				state.clickNum++
			} else {
				eventInfos.value[state.round - 1]['clickDate'].push(new Date().getTime())
			}
		}
	}
	const go = () => {
		redirectTo('/pages/login/index')
	}
	const back = () => {
		redirectTo('/pages/ivacpt/index')
	}
</script>

<style lang="scss">
	.iva {
		width: 100vw;
		height: 100vh;
		padding-top: 70rpx;
		position: fixed;
		z-index: 1;



		&-step {
			width: 120%;
			position: absolute;
			left: 50%;
			transform: translateX(-50%);
		}

		&-content {
			height: 84vh;
			width: 100%;
			position: absolute;
			top: 16%;
			padding: 32rpx;

			&-box {
				display: flex;
				flex-direction: column;
				align-items: center;

				&-tril {
					font-size: 40rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: bold;
					color: #FFAB01;
				}

				&-rule {
					display: flex;
					flex-direction: column;
					align-items: center;

					&-img {
						width: 488rpx;
						margin-top: 60rpx;
					}

					&-text {
						font-size: 48rpx;
						font-family: SourceHanSansCN-Bold, SourceHanSansCN;
						font-weight: bold;
						color: #ffffff;
						margin-top: 40rpx;
						margin-bottom: 140rpx;
					}

					&-btn {
						width: 628rpx;
						height: 120rpx;
						background: #287FFF;
						border-radius: 74rpx;
						font-size: 40rpx;
						font-family: SourceHanSansCN-Bold, SourceHanSansCN;
						font-weight: bold;
						color: #FFFFFF;
					}
				}

				&-tips {
					font-size: 54rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: bold;
					color: #FFAB01;
					position: relative;

					&-five {
						position: absolute;
						right: -180rpx;
						width: 200rpx;
						top: 50%;
						transform: translateY(-50%);
					}
				}

				&-value {
					width: 619rpx;
					height: 748rpx;
					border-radius: 30rpx;
					border: 6rpx solid #F2B103;
					margin-top: 26rpx;
					margin-bottom: 80rpx;

					&-other {
						width: 581rpx;
						height: 710rpx;
						border-radius: 30rpx;
						border: 6rpx solid #F2B103;

						&-other1 {
							width: 544rpx;
							height: 673rpx;
							border-radius: 30rpx;
							border: 6rpx solid #F2B103;
							font-family: SourceHanSansCN, SourceHanSansCN;
							font-weight: 500;
							font-size: 600rpx;
							color: #00B210;
						}
					}
				}

				&-title {
					font-size: 52rpx;
					font-family: SourceHanSansCN-Medium, SourceHanSansCN;
					font-weight: 500;
					color: #FFFFFF;
					display: flex;
					align-items: center;
					width: 92%;


					&-icon {
						border-radius: 50%;
						width: 60rpx;
						height: 60rpx;
						background: #FFFFFF;
						color: #287FFF;
						margin-right: 10rpx;
						flex-shrink: 0;
					}
				}
			}
		}
	}
</style>