<template>
	<view class="assessment-project-page">
		<!-- 状态栏 -->
		<view class="status-bar"></view>
		
		<!-- 顶部功能区 -->
		<view class="header">
			<view class="header-bg"></view>
			<!-- 返回按钮 -->
			<view class="back-btn" @click="goBack">
				<image src="/static/nav-back-icon.png" class="back-icon"></image>
			</view>
			<text class="instruction">请选择测评项目</text>
		</view>
		
		<!-- 选项卡组合 -->
		<view class="tab-container" v-if="respondentGroups.length > 0">
			<view class="tab-group">
				<view class="tab-item" 
					v-for="(group, index) in respondentGroups" 
					:key="group.respondentGroupType"
					:class="{ active: activeTabIndex === index }" 
					@click="switchTab(index)">
					<text class="tab-text">{{ group.respondentGroupName }}</text>
				</view>
			</view>
		</view>
		
		<!-- 项目卡片容器 - 孩子部分独立布局 -->
		<view class="child-section-container" v-if="currentTabData.length > 0 && currentGroup && currentGroup.respondentGroupType === 'C'">
			<!-- 测验类独立区域 -->
			<view class="section-box left-section" v-if="testProjects.length > 0">
				<view class="section-title">测验类</view>
				<view class="project-cards">
											<view class="project-card" 
							v-for="item in testProjects" 
							:key="item.projectId"
							@click="startProject(item)">
							<view class="card-content">
								<view class="project-icon test-icon" :style="{ backgroundImage: item.iconUrl ? `url(${item.iconUrl})` : '' }">
								</view>
							<view class="project-info">
								<text class="project-name">{{ item.projectName }}</text>
								<text class="project-type">{{ item.simpleDesc || '' }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 量表评估类独立区域 -->
			<view class="section-box right-section" v-if="questProjects.length > 0">
				<view class="section-title">量表评估类</view>
				<view class="project-cards">
											<view class="project-card" 
							v-for="item in questProjects" 
							:key="item.projectId"
							@click="startProject(item)">
							<view class="card-content">
								<view class="project-icon quest-icon" :style="{ backgroundImage: item.iconUrl ? `url(${item.iconUrl})` : '' }">
								</view>
							<view class="project-info">
								<text class="project-name">{{ item.projectName }}</text>
								<text class="project-type">{{ item.simpleDesc || '' }}</text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 家长和教师部分的统一容器 -->
		<view class="project-container" v-if="currentTabData.length > 0 && currentGroup && currentGroup.respondentGroupType !== 'C'">
			<view class="project-grid">
				<view class="project-card" 
					v-for="item in currentTabData" 
					:key="item.projectId"
					@click="startProject(item)">
					<view class="card-content">
						<view class="project-icon quest-icon" :style="{ backgroundImage: item.iconUrl ? `url(${item.iconUrl})` : '' }">
						</view>
						<view class="project-info">
							<text class="project-name">{{ item.projectName }}</text>
							<text class="project-type">{{ item.simpleDesc || '' }}</text>
						</view>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<text class="loading-text">加载中...</text>
		</view>
		
		<!-- 空状态 -->
		<view class="empty-container" v-if="!loading && respondentGroups.length === 0">
			<text class="empty-text">暂无测评项目</text>
		</view>
	</view>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import httpRequest from '@/utils/interceptors.js'

// 响应式数据
const loading = ref(false)
const activeTabIndex = ref(0)
const respondentGroups = ref([])

// 计算属性
const currentGroup = computed(() => {
	if (respondentGroups.value && respondentGroups.value[activeTabIndex.value]) {
		return respondentGroups.value[activeTabIndex.value]
	}
	return null
})

const currentTabData = computed(() => {
	if (currentGroup.value) {
		return currentGroup.value.detailProjectInfoList || []
	}
	return []
})

// 孩子部分的测验类项目（非QUEST类型）
const testProjects = computed(() => {
	if (currentGroup.value && currentGroup.value.respondentGroupType === 'C') {
		return currentTabData.value.filter(item => item.quizType !== 'QUEST')
	}
	return []
})

// 孩子部分的量表评估项目（QUEST类型）
const questProjects = computed(() => {
	if (currentGroup.value && currentGroup.value.respondentGroupType === 'C') {
		return currentTabData.value.filter(item => item.quizType === 'QUEST')
	}
	return []
})

// 方法
const loadProjectList = async (keepCurrentTab = false) => {
	// 保存当前选中的标签页索引
	const currentTabIndex = activeTabIndex.value
	
	loading.value = true
	try {
		const response = await httpRequest.post('project/v1.1/qryConceptualProjectList', {})
		
		if (response.code === '0000' && response.data) {
			respondentGroups.value = response.data
			
			// 设置标签页选中状态
			if (respondentGroups.value.length > 0) {
				if (keepCurrentTab && currentTabIndex < respondentGroups.value.length) {
					// 保持当前选中的标签页
					activeTabIndex.value = currentTabIndex
				} else {
					// 设置默认选中第一个标签页
					activeTabIndex.value = 0
				}
			}
		} else {
			uni.showToast({
				title: response.msg || '加载失败',
				icon: 'none'
			})
		}
	} catch (error) {
		console.error('加载测评项目失败:', error)
		uni.showToast({
			title: '网络错误，请重试',
			icon: 'none'
		})
	} finally {
		loading.value = false
	}
}

const switchTab = (tabIndex) => {
	activeTabIndex.value = tabIndex
}

const goBack = () => {
	uni.navigateBack({
		delta: 1
	})
}

const getProjectIcon = (quizType) => {
	switch (quizType) {
		case 'CPT-STD':
		case 'CPT-MINI':
			return '🧠'
		case 'PPVT':
			return '🎯'
		case 'QUEST':
		default:
			return '📋'
	}
}

const getProjectIconClass = (quizType) => {
	switch (quizType) {
		case 'CPT-STD':
			return 'iva-cpt'
		case 'CPT-MINI':
			return 'iva-cpt-mini'
		case 'PPVT':
			return 'ppvt'
		default:
			return ''
	}
}

const getProjectTypeText = (quizType) => {
	switch (quizType) {
		case 'CPT-STD':
			return 'IVA-CPT标准版'
		case 'CPT-MINI':
			return 'IVA-CPT MINI'
		case 'PPVT':
			return 'PPVT测评'
		case 'QUEST':
		default:
			return '量表评估'
	}
}

const startProject = (item) => {
	console.log('开始测评项目:', item)
	
	// 根据 quizType 跳转到不同的页面
	const quizType = item.quizType
	
	switch (quizType) {
		case 'CPT-STD':
			// IVA-CPT 标准版
			uni.navigateTo({
				url: `/pages/ivacpt/introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}`
			})
			break
		case 'CPT-MINI':
			// IVA-CPT MINI版
			uni.navigateTo({
				url: `/pages/ivacpt/introduce?type=mini&projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}`
			})
			break
		case 'PPVT':
			// PPVT 测评
			uni.navigateTo({
				url: `/pages/introduce/index?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}`
			})
			break
		case 'QUEST':
		default:
			// 量表评估 - 跳转至 SNAP-IV 量表介绍页
			uni.navigateTo({
				url: `/pages/scale/snap-iv-introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}`
			})
			break
	}
}

// 生命周期
onMounted(() => {
	console.log('新测评项目页面已加载')
	loadProjectList()
})

onShow(() => {
	console.log('新测评项目页面显示')
	// 页面显示时刷新数据，保持当前选中的标签页
	loadProjectList(true)
	
	// #ifdef APP-PLUS
	// 设置横屏
	setTimeout(() => {
		plus.screen.lockOrientation('landscape-primary')
	}, 100)
	// #endif
})
</script>

<style lang="scss" scoped>
.assessment-project-page {
	width: 100vw;
	height: 100vh;
	background: #F6F6F6;
	display: flex;
	flex-direction: column;
	overflow: hidden; /* 保持页面整体不滚动，由内容区域内部滚动 */
	
	/* 确保页面布局固定 */
	position: relative;
}

/* 顶部功能区 - 与测评记录页面保持一致 */
.header {
	position: relative;
	width: 100%;
	height: 88px;
	flex-shrink: 0;
	
	.header-bg {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: #FFFFFF;
	}
	
	/* 返回按钮 */
	.back-btn {
		position: absolute;
		left: 40px;
		top: 28px;
		width: 32px;
		height: 32px;
		display: flex;
		align-items: center;
		justify-content: center;
		cursor: pointer;
		z-index: 10;
		
		.back-icon {
			width: 24px;
			height: 24px;
		}
	}
	
	.instruction {
		position: absolute;
		left: 50%;
		top: 28px;
		transform: translateX(-50%);
		font-family: 'Alibaba PuHuiTi';
		font-size: 32px;
		color: #333333;
		font-weight: 400;
		line-height: 32px;
	}
}

/* 选项卡组合 - 调整间距以适配新布局 */
.tab-container {
	display: flex;
	justify-content: flex-start;
	align-items: flex-start;
	margin: 20px 40px 0 40px;
	background: transparent;
	border-radius: 0;
	border-bottom: none;
	padding: 0;
	
	.tab-group {
		display: flex;
		gap: 16px; /* 从11px调整为16px */
	}
	
	.tab-item {
		display: flex;
		align-items: center;
		padding: 20px 24px; /* 从14px 17px调整为20px 24px */
		border-radius: 16px 16px 0 0; /* 从11px调整为16px */
		background: #F3F8FF;
		min-width: auto; /* 从固定宽度改为自适应 */
		justify-content: center;
		cursor: pointer;
		border-bottom: 2px solid #EEEEEE;
		
		&.active {
			background: #287FFF;
			border-bottom: none;
			
			.tab-text {
				color: #FFFFFF;
			}
		}
		
		.tab-text {
			font-family: 'Alibaba PuHuiTi';
			font-size: 28px; /* 从19px调整为28px */
			color: #287FFF;
			font-weight: 500; /* 从400调整为500 */
			line-height: 1em;
			white-space: nowrap;
		}
	}
}

/* 项目容器 - 调整以适配新布局 */
.project-container {
	flex: 1 1 0%;
	min-height: 0;
	margin: 0 40px;
	margin-top: 16px;
	background: #FFFFFF;
	border-radius: 0 0 8px 8px;
	padding: 40px;
	overflow-y: auto;
	overflow-x: hidden;
	
	/* 优化滚动显示 */
	scroll-behavior: smooth;
	-webkit-overflow-scrolling: touch;
	
	/* 防止滚动条遮挡内容 */
	padding-bottom: 20px;
	
	/* 确保内容区域可以正确滚动 */
	max-height: calc(100vh - 88px - 80px); /* 减去头部和选项卡的高度（已移除状态栏） */
	
	/* 响应式设计 */
	@media screen and (max-width: 768px) {
		margin: 0 20px;
		margin-top: 12px;
		padding: 24px;
		border-radius: 0 0 8px 8px;
		padding-bottom: 16px;
		overflow-y: auto;
		overflow-x: hidden;
		max-height: calc(100vh - 88px - 60px);
	}
	
	@media screen and (max-width: 480px) {
		margin: 0 16px;
		margin-top: 8px;
		padding: 16px;
		border-radius: 0 0 6px 6px;
		padding-bottom: 12px;
		overflow-y: auto;
		overflow-x: hidden;
		max-height: calc(100vh - 88px - 40px);
	}
}

/* 孩子部分的独立布局 - 调整以适配新布局 */
.child-section-container {
	display: flex;
	gap: 17px;
	margin: 0 40px;
	margin-top: 16px;
	flex: 1 1 0%;
	min-height: 0;
	
	.section-box {
		flex: 1;
		background: #FFFFFF;
		border-radius: 0 0 8px 8px;
		padding: 40px;
		overflow-y: auto;
		overflow-x: hidden;
		
		/* 优化滚动显示 */
		scroll-behavior: smooth;
		-webkit-overflow-scrolling: touch;
		
		/* 防止滚动条遮挡内容 */
		padding-bottom: 20px;
		
		/* 限制最大高度，确保内容可以滚动 */
		max-height: calc(100vh - 88px - 80px); /* 减去头部和选项卡的高度（已移除状态栏） */
		
		/* 左右区域的特殊样式可以在这里添加 */
		
		.section-title {
			font-family: 'Alibaba PuHuiTi';
			font-size: 32px;
			font-weight: 400;
			color: #333333;
			margin-bottom: 32px;
			text-align: left;
			padding-bottom: 12px;
			border-bottom: 1px solid #EEEEEE;
		}
		
		.project-cards {
			display: flex;
			flex-direction: column;
			gap: 32px;
		}
	}
	
	/* 响应式设计 */
	@media screen and (max-width: 768px) {
		gap: 12px;
		margin: 0 20px;
		margin-top: 12px;
		
		.section-box {
			padding: 24px;
			border-radius: 0 0 8px 8px;
			padding-bottom: 16px;
			overflow-y: auto;
			overflow-x: hidden;
			max-height: calc(100vh - 88px - 60px);
		}
	}
	
	@media screen and (max-width: 480px) {
		flex-direction: column;
		gap: 8px;
		margin: 0 16px;
		margin-top: 8px;
		
		.section-box {
			padding: 16px;
			border-radius: 0 0 6px 6px;
			padding-bottom: 12px;
			overflow-y: auto;
			overflow-x: hidden;
			max-height: calc(100vh - 48px - 88px - 40px);
		}
	}
}

/* 家长和教师部分的网格显示 */
.project-grid {
	display: grid;
	grid-template-columns: 1fr 1fr;
	gap: 32px; /* 从20px调整为32px */
	padding: 32px 0; /* 从20px调整为32px */
	
	/* 响应式设计 */
	@media screen and (max-width: 768px) {
		grid-template-columns: 1fr;
		gap: 24px;
		padding: 24px 0;
	}
	
	@media screen and (max-width: 480px) {
		gap: 16px;
		padding: 16px 0;
	}
}

/* 项目卡片 - 根据Figma设计调整 */
.project-card {
	background: #FFFFFF;
	border: 1px solid #EEEEEE; /* 从#E8E8E8调整为#EEEEEE */
	border-radius: 12px; /* 圆角调整为12px */
	padding: 0; /* 移除内边距，改为内部元素控制 */
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: none; /* 移除默认阴影 */
	overflow: hidden;
	
	&:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
		border-color: #287FFF;
	}
	
	.card-content {
		display: flex;
		align-items: flex-start;
		gap: 20px; /* 从16px调整为20px */
		padding: 32px; /* 移到这里控制内边距 */
		
		.project-icon {
			width: 140px; /* 从60px调整为140px，匹配Figma设计 */
			height: 140px;
			background: #FFFFFF;
			border: 1px solid #EEEEEE;
			border-radius: 12px; /* 圆角调整为12px */
			display: flex;
			align-items: center;
			justify-content: center;
			flex-shrink: 0;
			position: relative;
			background-size: contain;
			background-repeat: no-repeat;
			background-position: center;
			
			&.quest-icon {
				background-color: #E6F4FF; /* 调整背景色，使用background-color而不是background */
			}
			
			&.test-icon {
				background-color: #FFFFFF;
			}
			
			.icon-text {
				font-size: 48px; /* 从28px调整为48px */
			}
		}
		
		.project-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			gap: 12px; /* 从6px调整为12px */
			padding-top: 8px;
			
			.project-name {
				font-family: 'Alibaba PuHuiTi';
				font-size: 28px; /* 从18px调整为28px */
				font-weight: 400; /* 从500调整为400 */
				color: #333333;
				line-height: 1.2;
			}
			
			.project-type {
				font-family: 'Alibaba PuHuiTi';
				font-size: 24px; /* 从14px调整为24px */
				color: #666666; /* 从#287FFF调整为#666666 */
				font-weight: 400;
				min-height: auto;
				line-height: 1.2;
			}
		}
	}
	
	/* 响应式设计 */
	@media screen and (max-width: 768px) {
		.card-content {
			gap: 16px;
			padding: 24px;
			
			.project-icon {
				width: 100px;
				height: 100px;
				border-radius: 8px;
			}
			
			.project-info {
				.project-name {
					font-size: 24px;
				}
				
				.project-type {
					font-size: 20px;
				}
			}
		}
	}
	
	@media screen and (max-width: 480px) {
		.card-content {
			gap: 12px;
			padding: 16px;
			
			.project-icon {
				width: 80px;
				height: 80px;
				border-radius: 6px;
			}
			
			.project-info {
				.project-name {
					font-size: 20px;
				}
				
				.project-type {
					font-size: 16px;
				}
			}
		}
	}
}

/* 加载状态 */
.loading-container {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 200px; /* 确保有足够的空间显示加载状态 */
	
	.loading-text {
		font-family: 'Alibaba PuHuiTi';
		font-size: 28px; /* 从18px调整为28px */
		color: #666666;
	}
	
	/* 响应式设计 */
	@media screen and (max-width: 768px) {
		.loading-text {
			font-size: 24px;
		}
	}
	
	@media screen and (max-width: 480px) {
		.loading-text {
			font-size: 20px;
		}
	}
}

/* 空状态 */
.empty-container {
	flex: 1;
	display: flex;
	align-items: center;
	justify-content: center;
	min-height: 200px; /* 确保有足够的空间显示空状态 */
	
	.empty-text {
		font-family: 'Alibaba PuHuiTi';
		font-size: 28px; /* 从18px调整为28px */
		color: #999999;
	}
	
	/* 响应式设计 */
	@media screen and (max-width: 768px) {
		.empty-text {
			font-size: 24px;
		}
	}
	
	@media screen and (max-width: 480px) {
		.empty-text {
			font-size: 20px;
		}
	}
}
</style>
