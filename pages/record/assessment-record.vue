<template>
    <view class="assessment-record-page responsive-container" @click="closeAllDropdowns">
        
        <!-- 顶部功能区 -->
        <view class="header">
            <view class="header-bg"></view>
            <view class="back-btn" @click="goBack">
                <view class="back-arrow"></view>
            </view>
            <text class="title">测评记录</text>
        </view>
        
        <!-- 筛选区域 -->
        <view class="filter-section">
            <!-- 搜索框 -->
            <view class="search-frame">
                <input type="text" v-model="searchText" placeholder="请输入患者名字/病案号查找" class="search-input" @confirm="onSearch" />
                <view class="search-addon">
                    <view class="search-icon"></view>
                </view>
            </view>
            
            <!-- 测评项目下拉 -->
            <view class="filter-select project-select" @click.stop="toggleProjectDropdown">
                <text class="filter-text" :class="{ active: !!filters.project }">{{ projectDisplayText }}</text>
                <view class="arrow-down"></view>
                <view class="dropdown" v-if="showProjectDropdown" @click.stop>
                    <view class="dropdown-item" :class="{ selected: !filters.project }" @click="selectProject('')">测评项目</view>
                    <view class="dropdown-item" :class="{ selected: filters.project === 'SNAP-IV' }" @click="selectProject('SNAP-IV')">SNAP-IV父母评定量表(26项)</view>
                    <view class="dropdown-item" :class="{ selected: filters.project === 'VADRS' }" @click="selectProject('VADRS')">VanderbiltADHD诊断评定量表(VADRS)</view>
                    <view class="dropdown-item" :class="{ selected: filters.project === 'BRIEF-P' }" @click="selectProject('BRIEF-P')">学龄前执行功能行为评定问卷(BRIEF-P)</view>
                    <view class="dropdown-item" :class="{ selected: filters.project === 'BRIEF' }" @click="selectProject('BRIEF')">学龄儿童执行功能行为评定问卷(BRIEF)</view>
                    <view class="dropdown-item" :class="{ selected: filters.project === 'Stroop' }" @click="selectProject('Stroop')">Stroop测验</view>
                </view>
            </view>
            
            <!-- 性别下拉 -->
            <view class="filter-select gender-select" @click.stop="toggleGenderDropdown">
                <text class="filter-text" :class="{ active: !!filters.gender }">{{ genderDisplayText }}</text>
                <view class="arrow-down"></view>
                <view class="dropdown" v-if="showGenderDropdown" @click.stop>
                    <view class="dropdown-item" :class="{ selected: !filters.gender }" @click="selectGender('')">性别</view>
                    <view class="dropdown-item" :class="{ selected: filters.gender === 'M' }" @click="selectGender('M')">男</view>
                    <view class="dropdown-item" :class="{ selected: filters.gender === 'F' }" @click="selectGender('F')">女</view>
                </view>
            </view>
            
            <!-- 完成时间选择器 -->
            <view class="date-picker" @click.stop="openDatePopover">
                <text class="date-text" :class="{ active: !!filters.completeDate }">{{ filters.completeDate || '完成时间' }}</text>
                <view class="calendar-icon-wrapper">
                    <view class="calendar-icon"></view>
                </view>
            </view>
            
            <!-- 来源下拉 -->
            <view class="filter-select source-select" @click.stop="toggleSourceDropdown">
                <text class="filter-text" :class="{ active: !!filters.source }">{{ sourceDisplayText }}</text>
                <view class="arrow-down"></view>
                <view class="dropdown" v-if="showSourceDropdown" @click.stop>
                    <view class="dropdown-item" :class="{ selected: !filters.source }" @click="selectSource('')">来源</view>
                    <view class="dropdown-item" :class="{ selected: filters.source === 'workorder' }" @click="selectSource('workorder')">测评工单</view>
                    <view class="dropdown-item" :class="{ selected: filters.source === 'individual' }" @click="selectSource('individual')">单独测评</view>
                </view>
            </view>
            
            <!-- 得分范围 -->
            <view class="score-range">
                <view class="score-input">
                    <input type="number" v-model="filters.minScore" placeholder="最小得分：请输入" class="score-field" />
                </view>
                <text class="range-separator">至</text>
                <view class="score-input">
                    <input type="number" v-model="filters.maxScore" placeholder="最大得分：请输入" class="score-field" />
                </view>
            </view>
            
            <!-- 年龄范围 -->
            <view class="age-range">
                <view class="age-input">
                    <input type="number" v-model="filters.minAge" placeholder="最小年龄：请输入" class="age-field" />
                </view>
                <text class="range-separator">至</text>
                <view class="age-input">
                    <input type="number" v-model="filters.maxAge" placeholder="最大年龄：请输入" class="age-field" />
                </view>
            </view>
            
            <!-- 按钮组 -->
            <view class="button-group">
                <view class="btn-reset" @click="onReset">重置</view>
                <view class="btn-search" @click="onSearch">搜索</view>
            </view>
        </view>
        
        <!-- 表格区域 -->
        <view class="table-section">
            <!-- 表格标题 -->
            <view class="table-title">
                <text class="title-text">测评工单列表</text>
            </view>
            
            <!-- 表格容器 -->
            <view class="table-container">
                <!-- 表格头部 -->
                <view class="table-header">
                    <view class="table-cell header-cell col-index">序号</view>
                    <view class="table-cell header-cell col-patient">患者信息</view>
                    <view class="table-cell header-cell col-project">测评项目</view>
                    <view class="table-cell header-cell col-time">完成时间</view>
                    <view class="table-cell header-cell col-result">结论</view>
                    <view class="table-cell header-cell col-source">来源</view>
                    <view class="table-cell header-cell col-action">操作</view>
                </view>
                
                <!-- 表格内容 -->
                <view class="table-body">
                    <!-- 加载状态 -->
                    <view v-if="loading" class="loading-row">
                        <view class="loading-text">加载中...</view>
                    </view>
                    <!-- 无数据状态 -->
                    <view v-else-if="tableData.length === 0" class="empty-row">
                        <view class="empty-text">暂无数据</view>
                    </view>
                    <!-- 数据行 -->
                    <view v-else class="table-row" v-for="(item, index) in tableData" :key="item.id || index">
                        <view class="table-cell col-index">{{ (pageIndex - 1) * pageSize + index + 1 }}</view>
                        <view class="table-cell col-patient">
                            <text class="patient-name">{{ item.patientName }} {{ item.gender === 'M' ? '男' : '女' }}/{{ item.age }}岁{{ item.months }}个月/{{ item.patientId }}</text>
                        </view>
                        <view class="table-cell col-project">
                            <view class="project-content">
                                <text class="project-name">{{ item.projectName }}</text>
                                <view class="status-tag" :class="item.status" v-if="item.status === 'canceled'">已撤销</view>
                            </view>
                        </view>
                        <view class="table-cell col-time">{{ item.completeTime }}</view>
                        <view class="table-cell col-result">{{ item.score }}分</view>
                        <view class="table-cell col-source">
                            <view class="source-content">
                                <text class="source-text">{{ item.source }}</text>
                            </view>
                        </view>
                        <view class="table-cell col-action">
                            <view class="action-btns">
                                <text class="action-btn" @click="viewReport(item, $event)">查看报告</text>
                                <view class="btn-divider"></view>
                                <text class="action-btn" @click="retestAssessment(item)">再次测评</text>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            
            <!-- 分页 -->
            <view class="pagination">
                <view class="pagination-left">
                    <text class="total-text">共{{ totalCount }}条</text>
                </view>
                <view class="pagination-controls">
                    <view class="page-size-selector">
                        <text class="page-size-text">{{ pageSize }}条/页</text>
                        <view class="page-size-arrow"></view>
                    </view>
                    <view class="page-btn" :class="{ disabled: pageIndex <= 1 }" @click="prevPage">
                        <view class="arrow-left"></view>
                    </view>
                    <view class="page-numbers">
                        <template v-for="page in visiblePages" :key="page">
                            <view v-if="page === '...'" class="page-ellipsis">···</view>
                            <view v-else class="page-number" :class="{ active: page === pageIndex }" @click="goToPage(page)">{{ page }}</view>
                        </template>
                    </view>
                    <view class="page-btn" :class="{ disabled: pageIndex >= totalPages }" @click="nextPage">
                        <view class="arrow-right"></view>
                    </view>
                </view>
                <view class="pagination-right">
                    <text class="goto-text">前往</text>
                    <view class="goto-input">
                        <input type="number" v-model="gotoPageInput" @confirm="gotoPage" class="goto-input-field" />
                    </view>
                    <text class="goto-text">页</text>
                </view>
            </view>
        </view>
        
        <!-- 日期选择弹窗 -->
        <view v-if="showDatePopover" class="date-popover" @click.stop :style="{ top: datePopoverPosition.top + 'px', left: datePopoverPosition.left + 'px' }">
            <view class="popover-header">
                <view class="nav-btn" @click="prevMonth">‹</view>
                <view class="ym-text">{{ popoverYear }}年{{ popoverMonth }}月</view>
                <view class="nav-btn" @click="nextMonth">›</view>
            </view>
            <view class="week-row">
                <text v-for="w in ['日','一','二','三','四','五','六']" :key="w" class="week-cell">{{ w }}</text>
            </view>
            <view class="day-grid">
                <view v-for="(cell, idx) in calendarCells" :key="idx"
                      class="day-cell"
                      :class="{ other: cell.isOtherMonth, selected: cell.fullDate === selectedTempDate }"
                      @click="onSelectDate(cell)">
                    {{ cell.day }}
                </view>
            </view>
        </view>
        
        <!-- 报告选项浮窗 -->
        <view class="report-modal" v-if="showReportModal" @click="closeReportModal">
            <view class="report-modal-content" @click.stop :style="{ top: reportModalPosition.top + 'px', left: reportModalPosition.left + 'px' }">
                <view class="report-option-item" 
                    v-for="(option, index) in reportOptions" 
                    :key="index"
                    @click="selectReportOption(option)">
                    <text class="option-text">{{ option.name }}</text>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import { initResponsive, getCurrentScale, responsiveMixin } from '@/utils/responsive.js'
import { qryAssessmentRecordList } from '@/service/index.js'
import { exportProjectEvaluationPdf } from '@/service/assessment.js'

export default {
    mixins: [responsiveMixin],
    data() {
        return {
            searchText: '',
            filters: {
                project: '',
                gender: '',
                completeDate: '',
                source: '',
                minScore: '',
                maxScore: '',
                minAge: '',
                maxAge: '',
                quizType: ''
            },
            showProjectDropdown: false,
            showGenderDropdown: false,
            showSourceDropdown: false,
            showDatePopover: false,
            popoverDate: new Date(),
            selectedTempDate: '',
            pageIndex: 1,
            pageSize: 10,
            totalPages: 1,
            totalCount: 0,
            gotoPageInput: 1,
            loading: false,
            tableData: [],
            // 报告选项浮窗相关
            showReportModal: false,
            reportOptions: [], // 报告选项列表
            reportModalPosition: { top: 0, left: 0 }, // 浮窗位置
            currentReportItem: null, // 当前点击的项目
            // 日期弹窗位置相关
            datePopoverPosition: { top: 0, left: 0 } // 日期弹窗位置
        }
    },
    async onShow() {
        //#ifdef APP-PLUS
        plus.navigator.setFullscreen(true);
        //#endif

        try {
            await initResponsive();
            console.log('页面响应式适配初始化完成, 当前缩放比例:', getCurrentScale());
        } catch (error) {
            console.error('响应式适配初始化失败:', error);
        }

        // 加载测评记录数据
        this.loadAssessmentRecords();
    },
    computed: {
        popoverYear() { return this.popoverDate.getFullYear() },
        popoverMonth() { return this.popoverDate.getMonth() + 1 },
        calendarCells() { return this.getMonthCells(this.popoverDate) },
        projectDisplayText() {
            const map = {
                'SNAP-IV': 'SNAP-IV父母评定量表',
                'VADRS': 'VanderbiltADHD诊断评定量表',
                'BRIEF-P': '学龄前执行功能行为评定问卷',
                'BRIEF': '学龄儿童执行功能行为评定问卷',
                'Stroop': 'Stroop测验'
            }
            return map[this.filters.project] || '测评项目'
        },
        genderDisplayText() {
            const map = { 'M': '男', 'F': '女' }
            return map[this.filters.gender] || '性别'
        },
        sourceDisplayText() {
            const map = { 'workorder': '测评工单', 'individual': '单独测评' }
            return map[this.filters.source] || '来源'
        },
        visiblePages() {
            const current = this.pageIndex
            const total = this.totalPages
            const pages = []
            
            if (total <= 7) {
                for (let i = 1; i <= total; i++) {
                    pages.push(i)
                }
            } else {
                if (current <= 4) {
                    for (let i = 1; i <= 5; i++) {
                        pages.push(i)
                    }
                    pages.push('...')
                    pages.push(total)
                } else if (current >= total - 3) {
                    pages.push(1)
                    pages.push('...')
                    for (let i = total - 4; i <= total; i++) {
                        pages.push(i)
                    }
                } else {
                    pages.push(1)
                    pages.push('...')
                    for (let i = current - 1; i <= current + 1; i++) {
                        pages.push(i)
                    }
                    pages.push('...')
                    pages.push(total)
                }
            }
            
            return pages
        }
    },
    methods: {
        goBack() {
            uni.navigateBack()
        },
        closeAllDropdowns() {
            this.showProjectDropdown = false
            this.showGenderDropdown = false
            this.showSourceDropdown = false
            this.showDatePopover = false
        },
        toggleProjectDropdown() {
            this.showProjectDropdown = !this.showProjectDropdown
            if (this.showProjectDropdown) {
                this.showGenderDropdown = false
                this.showSourceDropdown = false
            }
        },
        selectProject(project) {
            this.filters.project = project
            this.showProjectDropdown = false
        },
        toggleGenderDropdown() {
            this.showGenderDropdown = !this.showGenderDropdown
            if (this.showGenderDropdown) {
                this.showProjectDropdown = false
                this.showSourceDropdown = false
            }
        },
        selectGender(gender) {
            this.filters.gender = gender
            this.showGenderDropdown = false
        },
        toggleSourceDropdown() {
            this.showSourceDropdown = !this.showSourceDropdown
            if (this.showSourceDropdown) {
                this.showProjectDropdown = false
                this.showGenderDropdown = false
            }
        },
        selectSource(source) {
            this.filters.source = source
            this.showSourceDropdown = false
        },
        openDatePopover() {
            this.selectedTempDate = this.filters.completeDate || this.formatDate(new Date())
            this.popoverDate = this.filters.completeDate ? new Date(this.filters.completeDate) : new Date()
            
            // 计算完成时间按钮的位置
            this.$nextTick(() => {
                const query = uni.createSelectorQuery().in(this);
                query.select('.date-picker').boundingClientRect();
                query.exec((res) => {
                    if (res && res[0]) {
                        const rect = res[0];
                        // 设置弹窗位置：按钮正下方，左侧对齐
                        this.datePopoverPosition = {
                            top: rect.bottom + 8, // 按钮下方8px的间距
                            left: rect.left // 左侧对齐
                        };
                    }
                    this.showDatePopover = true;
                });
            });
        },
        formatDate(d) {
            const Y = d.getFullYear()
            const M = (d.getMonth() + 1).toString().padStart(2, '0')
            const D = d.getDate().toString().padStart(2, '0')
            return `${Y}-${M}-${D}`
        },
        getMonthCells(date) {
            const year = date.getFullYear()
            const month = date.getMonth()
            const firstDay = new Date(year, month, 1)
            const startWeek = firstDay.getDay()
            const daysInMonth = new Date(year, month + 1, 0).getDate()
            const prevDays = startWeek
            const cells = []
            
            if (prevDays > 0) {
                const prevLastDate = new Date(year, month, 0).getDate()
                for (let i = prevDays - 1; i >= 0; i--) {
                    const day = prevLastDate - i
                    const full = this.formatDate(new Date(year, month - 1, day))
                    cells.push({ day, fullDate: full, isOtherMonth: true })
                }
            }
            
            for (let d = 1; d <= daysInMonth; d++) {
                const full = this.formatDate(new Date(year, month, d))
                cells.push({ day: d, fullDate: full, isOtherMonth: false })
            }
            
            while (cells.length % 7 !== 0 || cells.length < 42) {
                const nextIndex = cells.length - (prevDays + daysInMonth) + 1
                const full = this.formatDate(new Date(year, month + 1, nextIndex))
                cells.push({ day: nextIndex, fullDate: full, isOtherMonth: true })
            }
            
            return cells
        },
        onSelectDate(cell) {
            this.selectedTempDate = cell.fullDate
            this.filters.completeDate = cell.fullDate
            this.showDatePopover = false
        },
        prevMonth() {
            const y = this.popoverDate.getFullYear()
            const m = this.popoverDate.getMonth()
            this.popoverDate = new Date(y, m - 1, 1)
        },
        nextMonth() {
            const y = this.popoverDate.getFullYear()
            const m = this.popoverDate.getMonth()
            this.popoverDate = new Date(y, m + 1, 1)
        },
        onReset() {
            this.searchText = ''
            this.filters = {
                project: '',
                gender: '',
                completeDate: '',
                source: '',
                minScore: '',
                maxScore: '',
                minAge: '',
                maxAge: '',
                quizType: ''
            }
            // 重置后重新加载数据
            this.pageIndex = 1
            this.loadAssessmentRecords()
        },
        onSearch() {
            console.log('搜索条件:', {
                searchText: this.searchText,
                filters: this.filters
            })
            // 搜索时重置页码
            this.pageIndex = 1
            this.loadAssessmentRecords()
        },
        prevPage() {
            if (this.pageIndex > 1) {
                this.pageIndex--
                this.loadAssessmentRecords()
            }
        },
        nextPage() {
            if (this.pageIndex < this.totalPages) {
                this.pageIndex++
                this.loadAssessmentRecords()
            }
        },
        goToPage(page) {
            if (page === '...' || page === this.pageIndex) return
            this.pageIndex = page
            this.loadAssessmentRecords()
        },
        gotoPage() {
            const page = parseInt(this.gotoPageInput)
            if (page >= 1 && page <= this.totalPages && page !== this.pageIndex) {
                this.pageIndex = page
                this.loadAssessmentRecords()
            }
        },
        async viewReport(item, event) {
            console.log('查看报告:', item);
            
            try {
                uni.showLoading({
                    title: '获取报告中...'
                });
                
                // 调用导出报告API
                const response = await exportProjectEvaluationPdf({
                    projectId: item.projectId,
                    evaluatId: item.id, // 使用测评记录的ID
                    traineeId: item.traineeId
                });
                
                uni.hideLoading();
                
                if (response.code === '0000' && response.data) {
                    const data = response.data;
                    const reportOptions = [];
                    
                    // 检查返回的文档类型
                    if (data.pdf) {
                        reportOptions.push({
                            type: 'pdf',
                            url: data.pdf,
                            name: 'PDF报告'
                        });
                    }
                    
                    if (data.doc) {
                        reportOptions.push({
                            type: 'doc',
                            url: data.doc,
                            name: 'Word报告'
                        });
                    }
                    
                    if (reportOptions.length === 0) {
                        uni.showToast({
                            title: '暂无报告文档',
                            icon: 'none'
                        });
                        return;
                    }
                    
                    if (reportOptions.length === 1) {
                        // 只有一个文档，直接打开
                        this.openDocument(reportOptions[0].url);
                    } else {
                        // 有多个文档，显示选择浮窗
                        this.currentReportItem = item;
                        this.reportOptions = reportOptions;
                        
                        // 计算浮窗位置
                        if (event && event.detail) {
                            // 使用点击事件的坐标信息
                            const query = uni.createSelectorQuery().in(this);
                            query.selectAll('.action-btn').boundingClientRect();
                            query.exec((res) => {
                                if (res && res[0] && res[0].length > 0) {
                                    // 获取所有按钮，找到最接近点击位置的按钮
                                    const buttons = res[0];
                                    let closestButton = null;
                                    let minDistance = Infinity;
                                    
                                    buttons.forEach(button => {
                                        const distance = Math.abs(
                                            Math.sqrt(
                                                Math.pow(event.detail.x - (button.left + button.width/2), 2) +
                                                Math.pow(event.detail.y - (button.top + button.height/2), 2)
                                            )
                                        );
                                        
                                        if (distance < minDistance) {
                                            minDistance = distance;
                                            closestButton = button;
                                        }
                                    });
                                    
                                    if (closestButton) {
                                        this.reportModalPosition = {
                                            top: closestButton.bottom + 5,
                                            left: closestButton.left
                                        };
                                    }
                                }
                            });
                        }
                        
                        this.showReportModal = true;
                    }
                } else {
                    throw new Error(response.msg || '获取报告失败');
                }
            } catch (error) {
                uni.hideLoading();
                console.error('获取报告详情失败:', error);
                uni.showToast({
                    title: '获取报告失败',
                    icon: 'none'
                });
            }
        },
        retestAssessment(item) {
            console.log('再次测评:', item)
            
            // 根据 quizType 字段跳转到不同的页面，QUEST 保持原有逻辑
            const quizType = item.quizType;

            if (quizType === 'QUEST' || !quizType) {
                // QUEST 类型或未定义时，保持原有逻辑 - 跳转至 SNAP-IV 量表介绍页
                uni.navigateTo({
                    url: `/pages/scale/snap-iv-introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${item.workOrderId}&traineeId=${item.traineeId}`
                })
            } else {
                // 其他类型根据 quizType 跳转
                switch (quizType) {
                    case 'CPT-STD':
                        // IVA-CPT 标准版 - 跳转至 IVA-CPT 介绍页
                        uni.navigateTo({
                            url: `/pages/ivacpt/introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${item.workOrderId}&traineeId=${item.traineeId}`
                        })
                        break;
                    case 'CPT-MINI':
                        // IVA-CPT MINI版 - 跳转至 IVA-CPT MINI 介绍页
                        uni.navigateTo({
                            url: `/pages/ivacpt/introduce?type=mini&projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${item.workOrderId}&traineeId=${item.traineeId}`
                        })
                        break;
                    case 'PPVT':
                        // PPVT 测评 - 跳转至 PPVT 介绍页
                        uni.navigateTo({
                            url: `/pages/introduce/index?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${item.workOrderId}&traineeId=${item.traineeId}`
                        })
                        break;
                    default:
                        // 未知类型，使用原有逻辑
                        console.warn('未知的 quizType:', quizType);
                        uni.navigateTo({
                            url: `/pages/scale/snap-iv-introduce?projectId=${item.projectId}&projectName=${encodeURIComponent(item.projectName)}&workOrderId=${item.workOrderId}&traineeId=${item.traineeId}`
                        })
                        break;
                }
            }
        },


        // 加载测评记录数据
        async loadAssessmentRecords() {
            if (this.loading) return

            this.loading = true

            try {
                // 构建请求参数
                const requestData = {
                    param: {
                        param: this.searchText || ""
                    },
                    pageSize: this.pageSize,
                    pageIndex: this.pageIndex
                }

                // 添加筛选条件
                if (this.filters.minScore) {
                    requestData.param.minScore = parseInt(this.filters.minScore)
                }
                if (this.filters.maxScore) {
                    requestData.param.maxScore = parseInt(this.filters.maxScore)
                }
                if (this.filters.source) {
                    requestData.param.evaluationSourceType = this.filters.source === 'workorder' ? '0' : '1'
                }
                if (this.filters.completeDate) {
                    // 将日期转换为13位时间戳
                    const date = new Date(this.filters.completeDate)
                    requestData.param.submitDate = date.getTime().toString()
                }
                if (this.filters.gender) {
                    requestData.param.sex = this.filters.gender
                }
                if (this.filters.minAge) {
                    requestData.param.minAge = parseInt(this.filters.minAge)
                }
                if (this.filters.maxAge) {
                    requestData.param.maxAge = parseInt(this.filters.maxAge)
                }
                if (this.filters.project) {
                    requestData.param.projectType = this.filters.project
                }
                // 添加 quizType 参数
                if (this.filters.quizType) {
                    requestData.param.quizType = this.filters.quizType
                }

                console.log('请求参数:', requestData)

                const response = await qryAssessmentRecordList(requestData)

                if (response && response.data) {
                    // 处理返回的数据
                    this.tableData = response.data.map(item => this.formatAssessmentRecord(item))
                    this.totalCount = response.total || 0
                    this.totalPages = Math.ceil(this.totalCount / this.pageSize)
                } else {
                    this.tableData = []
                    this.totalCount = 0
                    this.totalPages = 1
                }

            } catch (error) {
                console.error('加载测评记录失败:', error)
                uni.showToast({
                    title: '加载数据失败',
                    icon: 'none'
                })
                this.tableData = []
                this.totalCount = 0
                this.totalPages = 1
            } finally {
                this.loading = false
            }
        },

        // 格式化测评记录数据
        formatAssessmentRecord(item) {
            // 解析患者描述信息
            const traineeInfo = this.parseTraineeDesc(item.traineeSimDesc)

            // 格式化完成时间，固定为 YYYY-MM-dd HH:mm:ss
            const completeTime = item.createTime ? this.formatDateTime(new Date(item.createTime)) : ''

            return {
                id: item.evaluatId,
                patientName: traineeInfo.name,
                gender: traineeInfo.gender,
                age: traineeInfo.age,
                months: traineeInfo.months,
                patientId: traineeInfo.patientId,
                projectName: item.projectName,
                status: item.state === 'DONE' ? 'completed' : 'pending',
                completeTime: completeTime,
                score: item.score,
                source: item.evaluationSourceType === '0' ? '测评工单' : '单独测评',
                workOrderId: item.workOrderId,
                traineeId: item.traineeId,
                projectId: item.projectId
            }
        },

        // 工具：格式化为 YYYY-MM-dd HH:mm:ss
        formatDateTime(d) {
            const date = d instanceof Date ? d : new Date(d)
            if (isNaN(date.getTime())) return ''
            const Y = date.getFullYear()
            const M = (date.getMonth() + 1).toString().padStart(2, '0')
            const D = date.getDate().toString().padStart(2, '0')
            const h = date.getHours().toString().padStart(2, '0')
            const m = date.getMinutes().toString().padStart(2, '0')
            const s = date.getSeconds().toString().padStart(2, '0')
            return `${Y}-${M}-${D} ${h}:${m}:${s}`
        },

        // 解析患者描述信息
        parseTraineeDesc(desc) {
            // 格式: "张智轩(男)/7岁2月，61945454"
            const result = {
                name: '',
                gender: 'M',
                age: 0,
                months: 0,
                patientId: ''
            }

            if (!desc) return result

            try {
                // 分割姓名和其他信息
                const parts = desc.split('/')
                if (parts.length >= 2) {
                    // 解析姓名和性别
                    const nameGenderPart = parts[0]
                    const nameMatch = nameGenderPart.match(/^(.+)\(([男女])\)$/)
                    if (nameMatch) {
                        result.name = nameMatch[1]
                        result.gender = nameMatch[2] === '男' ? 'M' : 'F'
                    } else {
                        result.name = nameGenderPart
                    }

                    // 解析年龄和病案号
                    const agePatientPart = parts[1]
                    const ageMatch = agePatientPart.match(/(\d+)岁(\d+)月/)
                    if (ageMatch) {
                        result.age = parseInt(ageMatch[1])
                        result.months = parseInt(ageMatch[2])
                    }

                    // 解析病案号
                    const patientIdMatch = agePatientPart.match(/，(.+)$/)
                    if (patientIdMatch) {
                        result.patientId = patientIdMatch[1]
                    }
                }
            } catch (error) {
                console.error('解析患者描述失败:', error)
            }

            return result
        },

        // 打开文档
        openDocument(url) {
            try {
                // 如果是 http:// 开头，替换为 https://
                let secureUrl = url;
                if (url && url.startsWith('http://')) {
                    secureUrl = url.replace('http://', 'https://');
                    console.log('URL 已从 HTTP 替换为 HTTPS:', url, '->', secureUrl);
                }
                
                // 使用外部浏览器打开文档
                plus.runtime.openURL(secureUrl, (error) => {
                    if (error) {
                        console.error('外部浏览器打开失败:', error);
                        // 如果外部浏览器打开失败，尝试使用 uni.downloadFile 下载后打开
                        uni.downloadFile({
                            url: secureUrl,
                            success: (res) => {
                                if (res.statusCode === 200) {
                                    // 下载成功，调用外部程序打开文档
                                    uni.openDocument({
                                        filePath: res.tempFilePath,
                                        success: () => {
                                            console.log('文档打开成功');
                                        },
                                        fail: (error) => {
                                            console.error('文档打开失败:', error);
                                            uni.showToast({
                                                title: '文档打开失败',
                                                icon: 'none'
                                            });
                                        }
                                    });
                                }
                            },
                            fail: (error) => {
                                console.error('文档下载失败:', error);
                                uni.showToast({
                                    title: '文档下载失败',
                                    icon: 'none'
                                });
                            }
                        });
                    } else {
                        console.log('外部浏览器打开成功');
                    }
                });
            } catch (error) {
                console.error('打开文档失败:', error);
                uni.showToast({
                    title: '打开文档失败',
                    icon: 'none'
                });
            }
        },
        
        // 选择报告选项
        selectReportOption(option) {
            this.closeReportModal();
            this.openDocument(option.url);
        },
        
        // 关闭报告选项浮窗
        closeReportModal() {
            this.showReportModal = false;
            this.reportOptions = [];
            this.currentReportItem = null;
        }
    }
}
</script>

<style lang="scss" scoped>
@import '@/styles/responsive.scss';

.assessment-record-page {
    width: 100vw;
    height: 100vh;
    background: #F6F6F6;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 1000;
}

/* 状态栏 */
/* 已移除顶部状态栏 */

/* 顶部功能区 */
.header {
    position: relative;
    width: 100%;
    height: 88px;
    flex-shrink: 0;
    
    .header-bg {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #FFFFFF;
    }
    
    .back-btn {
        position: absolute;
        left: 40px;
        top: 28px;
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        
        .back-arrow {
            width: 9.33px;
            height: 18.67px;
            position: relative;
            
            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                width: 9.33px;
                height: 18.67px;
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9.33 18.67'%3E%3Cpath d='M8.33 1L1 9.33l7.33 8.34' stroke='%23333333' stroke-width='3' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center;
                background-size: contain;
            }
        }
    }
    
    .title {
        position: absolute;
        left: 50%;
        top: 28px;
        transform: translateX(-50%);
        font-family: 'Alibaba PuHuiTi';
        font-size: 32px;
        color: #333333;
        line-height: 32px;
    }
}

/* 筛选区域 */
.filter-section {
    width: calc(100% - 80px);
    margin: 0 40px;
    margin-top: 20rpx; /* 与顶部增加20rpx间距 */
    background: #FFFFFF;
    border-radius: 8px;
    padding: 24px;
    margin-bottom: 16px;
    flex-shrink: 0;
    
    display: grid;
    /* 第一行：搜索(略长) + 项目 + 完成时间 + 分数范围 + 性别 */
    /* 第二行：年龄范围(占两列) + 来源 + 按钮组(占三列，右对齐) */
    /* 列宽：搜索(弹性) | 项目(缩小为标准218的一半≈109px) | 完成时间(218px) | 分数范围(原252的2倍=504px) | 性别(218的一半≈109px) | 预留列 */
    grid-template-columns: 1.2fr 200px 218px 504px 109px 218px;
    grid-template-rows: auto auto;
    gap: 20rpx; /* 横纵间距约20rpx */
    align-items: center;
    
    .search-frame {
        grid-column: 1; /* 搜索区域稍长（1.2fr） */
        grid-row: 1;
        display: flex;
        align-items: center;
        border: 1px solid #EEEEEE;
        border-radius: 4px;
        background: #FFFFFF;
        padding-left: 16px;
        
        .search-input {
            height: 48px;
            flex: 1;
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #333333;
            border: none;
            outline: none;
            background: transparent;
            
            &::placeholder {
                color: #999999;
                font-size: 24px;
            }
        }
        
        .search-addon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-left: 1px solid #EEEEEE;
            border-radius: 0 4px 4px 0;
            margin-left: 8px;
            
            .search-icon {
                width: 29.09px;
                height: 29.09px;
                position: relative;
                
                &::before {
                    content: '';
                    position: absolute;
                    top: 3.64px;
                    left: 3.64px;
                    width: 19.64px;
                    height: 19.64px;
                    border: 1.2px solid #666666;
                    border-radius: 50%;
                }
                
                &::after {
                    content: '';
                    position: absolute;
                    bottom: 3.64px;
                    right: 3.64px;
                    width: 5.09px;
                    height: 5.09px;
                    background: #666666;
                    transform: rotate(45deg);
                    border-radius: 1px;
                }
            }
        }
    }
    
    .filter-select {
        background: #FFFFFF;
        border: 1px solid #EEEEEE;
        border-radius: 4px;
        padding: 12px 16px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        cursor: pointer;
        
        .filter-text {
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #999999;
            line-height: 24px;
            
            &.active {
                color: #333333;
            }
        }
        
        .arrow-down {
            width: 24px;
            height: 24px;
            position: relative;
            
            &::after {
                content: '';
                position: absolute;
                top: 9px;
                left: 4.5px;
                width: 15px;
                height: 9px;
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 15 9'%3E%3Cpath d='M1 1l6.5 6.5L14 1' stroke='%23999999' stroke-width='2' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center;
                background-size: contain;
            }
        }
        
        .dropdown {
            position: absolute;
            top: 100%;
            left: 0;
            width: 100%;
            background: #FFFFFF;
            border: 1px solid #EEEEEE;
            border-radius: 4px;
            box-shadow: 0 8px 20px rgba(0,0,0,0.12);
            margin-top: 8px;
            z-index: 20;
            max-height: 200px;
            overflow-y: auto;
            
            .dropdown-item {
                padding: 10px 12px;
                font-size: 24px;
                color: #333333;
                cursor: pointer;
                
                &.selected {
                    color: #287FFF;
                }
                
                &:hover {
                    background: #F6F6F6;
                }
            }
        }
    }
    
    .date-picker {
        background: #FFFFFF;
        border: 1px solid #EEEEEE;
        border-radius: 4px;
        padding: 12px 16px;
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        
        .date-text {
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #999999;
            line-height: 24px;
            
            &.active {
                color: #333333;
            }
        }
        
        .calendar-icon-wrapper {
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            
            .calendar-icon {
                width: 18px;
                height: 18px;
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 18 18'%3E%3Crect x='3' y='3' width='12' height='12' rx='1' stroke='%23999999' stroke-width='1.2' fill='none'/%3E%3Cpath d='M6 1v4M12 1v4M3 7h12' stroke='%23999999' stroke-width='1.2'/%3E%3C/svg%3E") no-repeat center;
                background-size: contain;
            }
        }
    }
    
    .score-range,
    .age-range {
        display: flex;
        align-items: center;
        gap: 20rpx; /* 行内元素间距约20rpx */
        
        .score-input,
        .age-input {
            background: #FFFFFF;
            border: 1px solid #EEEEEE;
            border-radius: 4px;
            height: 48px; /* 与搜索输入框高度保持一致 */
            padding: 0 16px; /* 垂直内边距归零，居中显示 */
            flex: 1;
            display: flex;
            align-items: center;
            
            .score-field,
            .age-field {
                width: 100%;
                height: 48px; /* 与搜索输入框高度一致 */
                font-family: 'Alibaba PuHuiTi';
                font-size: 24px;
                color: #333333;
                border: none;
                outline: none;
                background: transparent;
                
                &::placeholder {
                    color: #999999;
                    font-size: 24px;
                }
            }
        }
        
        .range-separator {
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #333333;
            flex-shrink: 0;
        }
    }
    
    /* 第一行排布（相邻间距20rpx） */
    .project-select { grid-column: 2; grid-row: 1; }
    .date-picker { grid-column: 3; grid-row: 1; }
    .score-range { grid-column: 4; grid-row: 1; }
    .gender-select { grid-column: 5; grid-row: 1; }

    /* 第二行排布 */
    .age-range { grid-column: 1 / 3; grid-row: 2; }
    .source-select { grid-column: 3; grid-row: 2; }
    .button-group {
        grid-column: 4 / 7; /* 跨三列，位于第二行右侧 */
        grid-row: 2;
        display: flex;
        gap: 20rpx;
        justify-content: flex-end;
        
        .btn-reset {
            height: 48px; /* 与输入框保持一致 */
            padding: 0 24px;
            background: #FFFFFF;
            border: 1px solid #C7C7C7;
            border-radius: 8px;
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #333333;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
        
        .btn-search {
            height: 48px; /* 与输入框保持一致 */
            padding: 0 24px;
            background: #287FFF;
            border-radius: 8px;
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #FFFFFF;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
    }
}

/* 表格区域 */
.table-section {
    flex: 1;
    margin: 0 40px;
    background: #FFFFFF;
    border-radius: 16px 16px 0 0;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
    
    .table-title {
        padding: 20px 24px;
        border-bottom: 1px solid #EEEEEE;
        flex-shrink: 0;
        
        .title-text {
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #333333;
        }
    }
}

.table-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    overflow: hidden;
}

.table-header {
    display: flex;
    background: #EEEEEE;
    flex-shrink: 0;
    
    .header-cell {
        font-family: 'Alibaba PuHuiTi';
        font-weight: 400;
        font-size: 24px;
        color: #333333;
        background: #EEEEEE;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px 12px;
        border-bottom: 1px solid #EEEEEE;
        border-right: 1px solid #EEEEEE;
        
        &:last-child {
            border-right: none;
        }
    }
}

.table-body {
    flex: 1;
    overflow-y: auto;
    min-height: 0;
    
    .table-row {
        display: flex;
        
        &:hover {
            background: #F8F9FA;
        }
    }
}

.table-cell {
    font-family: 'Alibaba PuHuiTi';
    font-size: 24px;
    color: #333333;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 9px 12px;
    border-bottom: 1px solid #EEEEEE;
    border-right: 1px solid #EEEEEE;
    min-height: 61px;
    
    &:last-child {
        border-right: none;
    }
}

/* 列宽设置 */
.col-index {
    width: 68px;
    flex-shrink: 0;
}

.col-patient {
    width: 379px;
    flex-shrink: 0;
    justify-content: flex-start;
    
    .patient-name {
        text-align: left;
        line-height: 1.2;
    }
}

.col-project {
    width: 554px;
    flex-shrink: 0;
    justify-content: flex-start;
    
    .project-content {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        
        .project-name {
            color: #287FFF;
            flex: 1;
        }
        
        .status-tag {
            padding: 8px 16px;
            border-radius: 8px;
            font-size: 24px;
            line-height: 1;
            flex-shrink: 0;
            
            &.canceled {
                background: #FFEFD2;
                color: #FFB130;
            }
        }
    }
}

.col-time {
    width: 265px;
    flex-shrink: 0;
}

.col-result {
    width: 118px;
    flex-shrink: 0;
}

.col-source {
    width: 200px;
    flex-shrink: 0;
    justify-content: flex-start;
    
    .source-content {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        
        .source-text {
            flex: 1;
        }
        

    }
}

.col-action {
    width: 283px;
    flex-shrink: 0;
    justify-content: flex-start;
    
    .action-btns {
        display: flex;
        align-items: center;
        gap: 8px;
        width: 100%;
        
        .action-btn {
            color: #287FFF;
            cursor: pointer;
            font-size: 24px;
            
            &:hover {
                text-decoration: underline;
            }
        }
        
        .btn-divider {
            width: 1px;
            height: 12px;
            background: #EEEEEE;
            flex-shrink: 0;
        }
    }
}

/* 分页 */
.pagination {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 12px 16px;
    border-top: 1px solid #EEEEEE;
    border-radius: 0 0 4px 4px;
    flex-shrink: 0;
    gap: 16px;
    
    .pagination-left {
        .total-text {
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #333333;
        }
    }
    
    .pagination-controls {
        display: flex;
        align-items: center;
        gap: 8px;
        
        .page-size-selector {
            display: flex;
            align-items: center;
            gap: 2px;
            padding: 8px;
            border: 1px solid #EEEEEE;
            border-radius: 4px;
            background: #FFFFFF;
            
            .page-size-text {
                font-family: 'Alibaba PuHuiTi';
                font-size: 24px;
                color: #333333;
            }
            
            .page-size-arrow {
                width: 16px;
                height: 16px;
                position: relative;
                
                &::after {
                    content: '';
                    position: absolute;
                    top: 6px;
                    left: 3px;
                    width: 9.6px;
                    height: 4.8px;
                    background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 9.6 4.8'%3E%3Cpath d='M1 1l3.8 2.8L8.6 1' stroke='%23999999' stroke-width='1.2' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center;
                    background-size: contain;
                }
            }
        }
        
        .page-btn {
            width: 32px;
            height: 32px;
            border: 1px solid #EEEEEE;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            background: #FFFFFF;
            
            &.disabled {
                opacity: 0.4;
                cursor: not-allowed;
            }
            
            .arrow-left,
            .arrow-right {
                width: 16px;
                height: 16px;
                position: relative;
                
                &::after {
                    content: '';
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 4.8px;
                    height: 9.6px;
                    background-size: contain;
                }
            }
            
            .arrow-left::after {
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4.8 9.6'%3E%3Cpath d='M3.8 1L1 4.8l2.8 3.8' stroke='%23999999' stroke-width='1.2' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center;
            }
            
            .arrow-right::after {
                background: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4.8 9.6'%3E%3Cpath d='M1 8.6l2.8-3.8L1 1' stroke='%23999999' stroke-width='1.2' fill='none' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E") no-repeat center;
            }
        }
        
        .page-numbers {
            display: flex;
            align-items: center;
            gap: 8px;
            
            .page-number {
                width: 32px;
                height: 32px;
                border: 1px solid #EEEEEE;
                border-radius: 4px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-family: 'Alibaba PuHuiTi';
                font-size: 24px;
                cursor: pointer;
                background: #FFFFFF;
                color: #333333;
                
                &.active {
                    background: #FFFFFF;
                    color: #287FFF;
                    border-color: #287FFF;
                }
            }
            
            .page-ellipsis {
                padding: 0 5px;
                font-size: 24px;
                color: #999999;
            }
        }
    }
    
    .pagination-right {
        display: flex;
        align-items: center;
        gap: 6px;
        
        .goto-text {
            font-family: 'Alibaba PuHuiTi';
            font-size: 24px;
            color: #333333;
        }
        
        .goto-input {
            width: 40px;
            height: 32px;
            border: 1px solid #EEEEEE;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #FFFFFF;
            
            .goto-input-field {
                width: 100%;
                height: 100%;
                border: none;
                background: transparent;
                text-align: center;
                font-family: 'Alibaba PuHuiTi';
                font-size: 24px;
                color: #333333;
            }
        }
    }
}

/* 日期选择弹窗 */
.date-popover {
    position: fixed;
    z-index: 9999;
    width: 280px;
    background: #FFFFFF;
    border: 1px solid #EEEEEE;
    border-radius: 8px;
    box-shadow: 0 8px 20px rgba(0,0,0,0.12);
    padding: 10px 10px 14px;
    
    .popover-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 8px;
        
        .nav-btn {
            width: 28px;
            height: 28px;
            border-radius: 4px;
            border: 1px solid #EEEEEE;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 18px;
            color: #333333;
        }
        
        .ym-text {
            font-size: 16px;
            color: #333333;
        }
    }
    
    .week-row {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 6px;
        margin-bottom: 6px;
        
        .week-cell {
            text-align: center;
            font-size: 12px;
            color: #999999;
        }
    }
    
    .day-grid {
        display: grid;
        grid-template-columns: repeat(7, 1fr);
        gap: 6px;
        
        .day-cell {
            height: 34px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            color: #333333;
            
            &.other {
                color: #C7C7C7;
            }
            
            &.selected {
                background: #287FFF;
                color: #FFFFFF;
            }
            
            &:hover:not(.selected) {
                background: #F0F0F0;
            }
        }
    }
}

/* 加载和空数据状态 */
.loading-row,
.empty-row {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    min-height: 200px;

    .loading-text,
    .empty-text {
        font-family: 'Alibaba PuHuiTi';
        font-size: 24px;
        color: #999999;
    }
}

/* 报告选项浮窗样式 */
.report-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 8888;
}

.report-modal-content {
    position: absolute;
    background: #FFFFFF;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border: 1px solid #E8E8E8;
    overflow: hidden;
    min-width: 120px;
}

.report-option-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: background-color 0.2s;
    
    &:hover {
        background: #F3F8FF;
    }
    
    &:not(:last-child) {
        border-bottom: 1px solid #F0F0F0;
    }
    
    .option-text {
        font-family: 'Alibaba PuHuiTi';
        font-size: 16px;
        color: #333333;
        font-weight: 400;
    }
}
</style>
