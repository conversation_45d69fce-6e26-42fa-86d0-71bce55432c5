<template>
	<view class="record">
		<!-- 顶部功能区 -->
		<view class="record-header">
			<view class="record-header-left" @click="go">
				<text class="iconfont">&#xe65f;</text>
				<text>首页</text>
			</view>
			<view class="record-header-center">
				<view class="search-box">
					<text class="iconfont search-icon">&#xe66d;</text>
					<input type="text" placeholder="请输入病案号或姓名" v-model="state.param.param" class="search-input" />
				</view>
			</view>
			<view class="record-header-right">
				<view class="btn-group">
					<button class="search-btn" @click="confirm">搜索</button>
					<button class="add-btn" @click="goAdd">添加患者</button>
				</view>
			</view>
		</view>

		<!-- 表格+分页 -->
		<view class="table-container">
			<!-- 表格 -->
			<view class="table-wrapper">
				<!-- 表头 -->
				<view class="table-header">
					<view v-for="(item,index) in title" :key="index" class="table-header-cell" :style="{width: item.width}">
						{{item.name}}
					</view>
				</view>
				
				<!-- 表格内容 -->
				<view class="table-body">
					<!-- 空状态 -->
					<view class="empty-state" v-if="!state.list.length">
						<view class="empty-icon">
							<text class="iconfont">&#xe678;</text>
						</view>
						<text class="empty-text">暂无就诊人信息</text>
					</view>
					
					<!-- 数据行 -->
					<view v-else>
						<view v-for="(item,index) in state.list" :key="index" class="table-row">
							<view class="table-cell" :style="{width: title[0].width}">{{item.serNum}}</view>
							<view class="table-cell" :style="{width: title[1].width}">{{item.patientName}}</view>
							<view class="table-cell" :style="{width: title[2].width}">{{item.sex===1?'男':'女'}}</view>
							<view class="table-cell" :style="{width: title[3].width}">{{item.age}}</view>
							<view class="table-cell" :style="{width: title[4].width}">{{getSch(item.stanOfCul)}}</view>
							<view class="table-cell" :style="{width: title[5].width}">{{item.remark||'未填写'}}</view>
							<view class="table-cell action-cell" :style="{width: title[6].width}">
								<button class="action-btn edit-btn" @click="()=>goEdit(item.outpatientId)">编辑</button>
								<button class="action-btn test-btn" @click="()=>goTest(item.outpatientId,item.patientName,item.year)">测评</button>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 分页 -->
			<view class="pagination">
				<view class="pagination-info">
					<text>共 {{state.total}} 条</text>
				</view>
				<view class="pagination-controls">
					<button class="page-btn" :disabled="state.pageIndex <= 1" @click="prevPage">
						<text class="iconfont">&#xe661;</text>
					</button>
					<view class="page-numbers">
						<button v-for="page in visiblePages" :key="page" 
							class="page-number" 
							:class="{active: page === state.pageIndex}"
							@click="goToPage(page)">
							{{page}}
						</button>
					</view>
					<button class="page-btn" :disabled="state.pageIndex >= totalPages" @click="nextPage">
						<text class="iconfont">&#xe660;</text>
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		reactive,
		watch
	} from 'vue';
	import {
		navigateTo,
		redirectTo,
		reLaunch,
		showLoading,
		hideLoading,
		getStorageSync
	} from '@/common/uniTool'
	import {
		getSch
	} from '../../common/method'
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		qryPatientListPage
	} from '../../service';
	import {
		useUserStore
	} from '../../stores/user';
	const userStore = useUserStore()
	const state = reactive({
		pageIndex: 1,
		pageSize: 10,
		status: 'more',
		total: 0,
		param: {
			param: ''
		},
		list: []
	})
	// 根据Figma设计调整表格列宽
	const title = [{
			name: '病案号',
			width: '200rpx'
		},
		{
			name: '姓名',
			width: '150rpx'
		},
		{
			name: '性别',
			width: '100rpx'
		},
		{
			name: '年龄',
			width: '120rpx'
		},
		{
			name: '教育程度',
			width: '160rpx'
		},
		{
			name: '临床诊断',
			width: '200rpx'
		},
		{
			name: '操作',
			width: '180rpx'
		}
	]

	onShow(() => {
		state.pageIndex = 1
		state.clickIndex = 0
		state.list = []
		getList()
		state.outpatientId && getDetailList(state.outpatientId)
	})
	watch(() => state.outpatientId, (outpatientId) => {
		getDetailList(outpatientId)
	})
	const downFile = async (item, index) => {
		if (item.sourceType === 'SNAP-IV') {
			navigateTo(`/pages/report/index?round=${item.round}&userName=${item.userName}&outpatientId=${item.outpatientId}`)
		} else {
			if (index === state.indexValue) {
				state.showPop = false
				state.fileList = null
				state.indexValue = -1
				return
			}
			let res
			if (item.sourceType === 'CPT-STD') {
				res = await exportIvacptStdPdf({
					evaluatId: item.evaluatId,
				})
				if (!res.data.pdf) {
					navigateTo(`/pages/webview/index?url=${reportUrl}#/ivacpt/${getStorageSync('token')}/${item.outpatientId}/${item.evaluatId}/PPVT/13`)
					return
				}
			} else if (item.sourceType === 'CPT-MINI') {
				res = await exportIvacptMiniPdf({
					evaluatId: item.evaluatId,
				})

			} else {
				res = await qryFreeEvaResultPDF({
					round: item.round,
					outpatientId: item.outpatientId,
					userName: item.userName
				})
			}

			// 确保返回的文档链接使用 HTTPS 协议
			const ensureHttps = (url) => {
				return typeof url === 'string' && /^http:\/\//i.test(url)
					? url.replace(/^http:\/\//i, 'https://')
					: url
			}
			if (res && res.data) {
				if (res.data.pdf) res.data.pdf = ensureHttps(res.data.pdf)
				if (res.data.doc) res.data.doc = ensureHttps(res.data.doc)
			}
			let list = Object.keys(res.data).filter(item => item != "outpatientId")
			state.downTypes = list
			state.showPop = true
			state.fileList = res.data
			state.indexValue = index
		}
	}
const openFile = (value) => {
    // 统一使用 HTTPS，处理中文文件名
    const ensureHttps = (url) => typeof url === 'string' && /^http:\/\//i.test(url) ? url.replace(/^http:\/\//i, 'https://') : url
    const url = ensureHttps(value)
    const encodedUrl = encodeURI(url)
    const fileType = /\.pdf(\?|$)/i.test(url) ? 'pdf' : (/\.docx?(\?|$)/i.test(url) ? 'docx' : undefined)

    uni.downloadFile({
        url: encodedUrl,
        success: function (res) {
            if (res.statusCode !== 200) {
                showToast('报告下载失败')
                return
            }
            let filePath = res.tempFilePath
            console.log(filePath)
            showLoading('生成报告中....')
            state.showPop = false

            const openLocal = (pathToOpen) => {
                uni.openDocument({
                    filePath: pathToOpen,
                    fileType,
                    showMenu: true,
                    success: function () {
                        hideLoading()
                    },
                    fail() {
                        hideLoading()
                        // 原生端降级尝试用系统打开远程链接
                        if (typeof plus !== 'undefined' && plus.runtime && url) {
                            try { plus.runtime.openURL(url) } catch (e) {}
                        }
                        showToast('打开失败')
                    }
                })
            }

            // 保存后强制重命名为 ASCII，规避中文/编码导致的打开失败
            const afterSaved = (savedPath) => {
                if (typeof plus !== 'undefined') {
                    plus.io.resolveLocalFileSystemURL(savedPath, (entry) => {
                        entry.getParent((parent) => {
                            const ext = fileType || (/\.(\w+)$/.exec(savedPath)?.[1] || 'pdf')
                            const newName = `report_${Date.now()}.${ext}`
                            entry.moveTo(parent, newName, (moved) => {
                                openLocal(moved.toLocalURL())
                            }, () => {
                                openLocal(savedPath)
                            })
                        }, () => openLocal(savedPath))
                    }, () => openLocal(savedPath))
                } else {
                    openLocal(savedPath)
                }
            }
            uni.saveFile({
                tempFilePath: filePath,
                success: ({ savedFilePath }) => afterSaved(savedFilePath),
                fail: () => afterSaved(filePath)
            })
        },
        fail: function () {
            showToast('报告下载失败')
        }
    })
}
	const goTest = (outpatientId, patientName, year) => {
		userStore.outpatientId = outpatientId
		userStore.userName = patientName
		userStore.year = year
		navigateTo('/pages/home/<USER>')
	}
	const goEdit = (outpatientId) => {
		navigateTo(`/pages/index/index?outpatientId=${outpatientId}`)
	}
	const getList = async () => {
		state.status = 'loading'
		let res = await qryPatientListPage({
			param: state.param,
			pageSize: 10,
			pageIndex: state.pageIndex
		})
		state.list.push(...res.data)
		if (res.data.length > 0) {
			state.outpatientId = state.list[0]['outpatientId']
		}
		state.total = res.total
		if (res.total <= state.list.length) {
			state.status = "no-more"
		} else {
			state.status = "more"
		}
	}
	const getDetailList = async (outpatientId) => {
		let res = await qryFreeEvaInfoList({
			outpatientId
		})
		state.userDetailList = res.data
	}
	const scrolltolower = () => {
		if (state.total <= state.list.length) {
			state.status = "no-more"
			return
		}
		state.pageIndex++
		getList()
	}
	const confirm = () => {
		state.pageIndex = 1
		state.clickIndex = 0
		state.list = []
		getList()
	}
	const goAdd = () => {
		navigateTo('/pages/index/index')
	}
	const checked = async (item, index) => {
		let res = await getDetailList(item['outpatientId'])
		state.clickIndex = index
	}
	const go = () => {
		reLaunch('/pages/home/<USER>')
	}
</script>

<style lang="scss">
	.record {
		width: 100vw;
		height: 100vh;
		display: flex;
		flex-direction: column;
		overflow: hidden;

		&-bottom {
			flex: 1;
			display: flex;
			align-items: center;

			&-right {
				width: 100%;
				height: 100%;
				background: #F6F6F6;
				padding: 30rpx;

				&-box {
					width: 100%;
					background: #FFFFFF;
					border-radius: 30rpx;
					overflow: hidden;
					height: 100%;

					&-bottom {
						display: flex;
						align-items: center;
						border-bottom: 4rpx solid #EEEEEE;

						&-item {
							height: 120rpx;
							display: flex;
							font-weight: bold;
							font-size: 34rpx;
							color: #111111;

							&-link {
								font-weight: bold;
								font-size: 38rpx;
								color: #3481FD;
								text-decoration-line: underline;
								text-decoration-color: #3481FD;
								position: relative;

								&-box {
									width: 169rpx;
									position: absolute;
									top: 56rpx;
									padding: 10rpx 10rpx;
									left: 14rpx;
									background: #FFFFFF;
									box-shadow: 0rpx 4rpx 20rpx 0rpx rgba(0, 0, 0, 0.25);
									border-radius: 8px;
									display: flex;
									align-items: center;
									flex-direction: column;
									font-size: 18px;
									font-family: PingFangSC, PingFang SC;
									font-weight: 400;
									color: #111111;
									z-index: 999;

									&-item {
										border-bottom: 1px solid #E3E3E3;
										margin-bottom: 8rpx;
										height: 60rpx;
									}
								}
							}
						}
					}

					&-top {
						height: 98rpx;
						background: #3481FD;
						display: flex;
						align-items: center;
						font-weight: bold;
						font-size: 38rpx;
						color: #FFFFFF;

						&-item {
							display: flex;
							align-items: center;
							text-align: center;
							justify-content: space-around;


						}
					}
				}
			}

			&-left {
				height: 100%;
				padding: 30rpx;
				display: flex;
				flex-direction: column;
				box-shadow: 11rpx 0 9rpx 0rpx rgba(17, 17, 17, 0.06);

				&-none {
					width: 100%;
					height: 100%;
					display: flex;
					flex-direction: column;
					align-items: center;
					justify-content: center;
					font-family: PingFangSC, PingFang SC;
					font-weight: bold;
					font-size: 38rpx;
					color: #999999;

					&-icon {
						width: 375rpx;
						height: 375rpx;
						background: #F9F9F9;
						border: 2rpx solid #EAEAEA;
						border-radius: 50%;
						margin-bottom: 90rpx;
						font-size: 190rpx;
						color: #EAEAEA;
						font-weight: normal;
					}
				}

				&-list {
					height: calc(86vh - 98rpx);

					&-item {
						height: 120rpx;
						display: flex;
						border-bottom: 4rpx solid #EEEEEE;
						font-weight: bold;
						font-size: 34rpx;
						color: #111111;

						&-click {
							width: 100%;
							background: linear-gradient(270deg, #EEF4FF 0%, #F9FCFF 100%);
							color: #3481FD;
							border-right: 8rpx solid #3481FD;
						}
					}
				}

				&-top {
					height: 98rpx;
					background: #3481FD;
					display: flex;
					align-items: center;
					font-weight: bold;
					font-size: 38rpx;
					color: #FFFFFF;

					&-item {
						display: flex;
						align-items: center;
						text-align: center;
						justify-content: space-around;

						&-link {
							font-weight: bold;
							font-size: 38rpx;
							color: #3481FD;
							border-bottom: 4rpx solid #3481FD;

						}
					}
				}
			}
		}

		&-top {
			height: 150rpx;
			background: #3481FD;
			width: 100%;
			display: flex;
			align-items: center;
			padding: 48rpx 74rpx;
			justify-content: space-around;

			&-left {
				font-weight: bold;
				font-size: 60rpx;
				color: #FFFFFF;

				&-icon {
					font-weight: normal;
				}
			}

			&-right {
				width: 296rpx;
				height: 90rpx;
				background: #FFFFFF;
				border-radius: 15rpx;
				font-weight: bold;
				font-size: 45rpx;
				color: #3481FD;

				&-icon {
					font-weight: normal;
					font-size: 80rpx;
				}
			}
		}
	}
</style>