<template>
	<view class="question">
		<progress :percent="100/30*state.countdown" activeColor="#10AEFF" stroke-width="20" />
		<view class="question-top center">
			<view class="question-top-title">
				第{{state.questionIndex}}题
			</view>
		</view>
		<view class="question-tips">
			<view class="question-tips-audio">
				<view class="question-tips-audio-box" @click="agagin">
					<text class="question-tips-audio-box-icon iconfont">&#xe6b3;</text>语言播报
				</view>
				<view class="question-tips-audio-text">
					{{state.nameList[state.questionIndex-1]}}
				</view>
			</view>
			<view class="question-tips-left">
				<text class="question-tips-left-icon iconfont">&#xe74f;</text>计时：{{formatSeconds(state.time)}}
			</view>
			<view class="question-tips-right">
				音量调节
				<slider class="question-tips-right-slider" max="1" min="0.01" step="0.01" @change="sliderChange" value="0.5" activeColor="#41A8DE" backgroundColor="rgba(0,0,0,0.05)"
					block-color="#FFFFFF" block-size="18" />
			</view>
		</view>
		<view class="question-content">
			<view class="question-content-img">
				<view class="question-content-img-item center" @click="clicked(index)" v-for="(item,index) in 4">
					<view class="question-content-img-item-box center" :class="index===state.clicked?'question-content-img-item-box-clicked':''">
						<image class="question-content-img-item-box-value" :src="`../../static/question/img-${state.questionIndex}-${index+1}.jpg`" mode="widthFix"></image>
					</view>
					<view class="question-content-img-item-text">
						图{{index+1}}
					</view>
				</view>

			</view>
			<view class="question-content-btn">
				<view class="question-content-btn-previous" v-if="state.questionIndex>1" @click="pre">
					上一题
				</view>
				<!-- 	<view class="question-content-btn-next" :class="state.clicked>=0&&'question-content-btn-next-click'" @click="go">
					下一题
				</view> -->
			</view>
		</view>
	</view>
</template>

<script setup>
	import {
		onMounted,
		onUnmounted,
		reactive,
		ref,
		watch
	} from "vue";
	import {
		onHide
	} from '@dcloudio/uni-app'
	import {
		formatSeconds
	} from "../../common/method";
	import {
		addFreeEvaInfo,
		qryFreeEvaResultPDF
	} from "../../service";
	import {
		navigateTo,
		redirectTo,
		getStorageSync,
		showLoading,
		hideLoading,
		showToast,
		reLaunch,
		navigateBack
	} from "../../common/uniTool";
	import {
		useUserStore
	} from "../../stores/user";
	
	const props = defineProps(['projectId', 'projectName', 'workOrderId', 'traineeId'])
	const userStore = useUserStore()
	const innerAudioContextRef = ref(null) //音频
	const timeRef = ref(null)
	const timeAddRef = ref(null)
	const falseAnswerRef = ref([])
	const state = reactive({
		imgList: ['../../static/1.png', '../../static/2.png',
			'../../static/3.png', '../../static/4.png'
		],
		clicked: -1,
		percent: 100,
		time: 0,
		countdown: 30,
		clickTime: new Date(),
		resetTime: null,
		audioTime: null,
		isClick: true,
		answer: [],
		questionIndex: 1,
		falseAnswer: [],
		falseSix: 0,
		trueAnswer: [4, 3, 1, 1, 4, 3, 3, 1, 4, 2, 3, 4, 4, 2, 2, 1, 4, 1, 1, 1, 3, 3, 1, 3, 4, 1, 4, 2, 4, 2, 1, 3, 2, 3, 1, 1, 4, 4, 4, 4, 2, 1, 2, 1, 3, 3, 3, 2, 1, 4, 4, 2, 2, 4, 3, 3, 3, 2,
			1, 2, 1, 3, 1, 1, 1, 2, 4, 2, 3, 4, 4, 2, 3, 2, 4, 4, 2, 3, 2, 1, 1, 1, 1, 1, 1, 1, 1, 4, 1, 1, 3, 3, 4, 1, 1, 1, 2, 3, 3, 1, 3, 3, 1, 2, 3, 1, 3, 2, 1, 3, 2, 1, 4, 4, 3, 3, 3, 2,
			2, 2
		],
		nameList: ['汽车', '母牛', '球', '小孩', '手指', '鸡', '热水瓶', '钥匙', '蜜蜂', '风扇', '叶子', '吹', '拖拉机', '孔雀', '女孩', '河流', '罐头', '裙子', '鼓', '信封', '指挥', '皇后', '徽章', '放映机', '打结', '鞭', '捉', '转盘', '车厢', '教师',
			'挖', '箭', '木棍', '装配', '缝', '船长', '网', '漏斗', '潜艇', '香肠', '采', '倒', '砂锅', '信号', '豆荚', '鹰', '风镜', '搓', '栏杆', '一群', '帆', '羚羊', '演说家',
			'升起',
			'宣誓',
			'豁口',
			'刻度',
			'通讯',
			'柜台',
			'袋鼠',
			'胶囊',
			'事故',
			'体育场',
			'挖掘',
			'绿洲',
			'警戒',
			'垄沟',
			'蜂巢',
			'阻挡',
			'攻击',
			'化学家',
			'障碍',
			'家庭用具',
			'驮',
			'学者',
			'礼节',
			'旅行',
			'同心圆',
			'旋管',
			'争论',
			'上钩',
			'华表',
			'浸泡',
			'运输',
			'横梁',
			'同胞',
			'平衡',
			'商标',
			'雕塑',
			'窗帷',
			'失望',
			'切线',
			'沉思',
			'绝技',
			'犬',
			'奇迹',
			'鉴定',
			'北极',
			'惊讶',
			'两栖类',
			'巧妙',
			'清晰',
			'恐怖',
			'象形文字',
			'畏怯',
			'出纳',
			'致意',
			'幽灵',
			'项',
			'溪谷',
			'遗传学',
			'雄辩',
			'约束',
			'诱饵',
			'建筑',
			'奔泻',
			'私语',
			'任性',
			'图案',
			'盘旋'
		]
	})

	onMounted(() => {
		innerAudioContextRef.value = uni.createInnerAudioContext()
		innerAudioContextRef.value.src = `../../static/audio/MP3-1.mp3`
		innerAudioContextRef.value.onEnded(function (res) {
			innerAudioContextRef.value.destroy() //先销毁音频
			innerAudioContextRef.value = null // 再赋值为null
			innerAudioContextRef.value = uni.createInnerAudioContext() // 最后再创建一个新的
		})
		setTimeout(() => {
			innerAudioContextRef.value.play()
		}, 500)
		timeRef.value = setInterval(() => {
			state.time++
		}, 1000)

		timeAdd()
	})
	onUnmounted(() => {
		clearInterval(timeAddRef.value)
		clearInterval(timeRef.value)
		if (innerAudioContextRef.value) {
			innerAudioContextRef.value.destroy()
			innerAudioContextRef.value = null
		}
		timeAddRef.value = null
		timeRef.value = null
	})
	const timeAdd = () => {
		timeAddRef.value = setInterval(() => {
			state.countdown--
		}, 1000)
	}
	const agagin = () => {
		innerAudioContextRef.value.stop()
		innerAudioContextRef.value.play()
	}
	const go = () => {
		if (!state.isClick) {
			return
		}
		if (state.audioTime) {
			clearTimeout(state.audioTime)
			state.audioTime = null
		}
		state.answer.push(`${state.trueAnswer[state.questionIndex-1]},${0},${new Date()-state.clickTime}`)
		reset()
	}
	//8个内累计错6个结束
	watch(() => state.falseAnswer, (falseAnswer) => {
		if (falseAnswer.length >= 8) {
			let num = falseAnswer.slice(falseAnswer.length - 8, falseAnswer.length).filter(item => item == 'false').length
			if (num >= 6) {
				clearTimeout(state.resetTime)
				gameover()
			}
		}
	}, {
		deep: true
	})
	//连续错六个结束
	watch(() => state.falseSix, (falseSix) => {
		if (falseSix >= 6) {
			clearTimeout(state.resetTime)
			gameover()
		}
	}, {
		deep: true
	})
	watch(() => state.countdown, (countdown) => {
		if (countdown === 0) {
			state.falseSix++
			state.falseAnswer.push('false')
			state.answer.push(`${state.trueAnswer[state.questionIndex-1]},${0},${new Date()-state.clickTime}`)
			reset()
		}
	})
	const gameover = () => {
		console.log('游戏结束');
		innerAudioContextRef.value.stop()
		state.isClick = false
		clearInterval(timeAddRef.value)
		clearInterval(timeRef.value)
		timeAddRef.value = null
		timeRef.value = null
		addFreeEvaInfo({
			evaConsumTime: state.time,
			outpatientId: userStore.outpatientId,
			signalData: state.answer,
			userName: userStore.userName,
			projectId: props.projectId,
			workorderId: props.workOrderId
		}).then(res => {
			console.log("res: " + JSON.stringify(res));
			qryFreeEvaResultPDF({
				outpatientId: userStore.outpatientId,
				userName: userStore.userName,
				round: res.data.round,
			}).then(res => {
				showLoading('生成报告中....')
				console.log("res: " + JSON.stringify(res));
				console.log(res.data.pdf);
				uni.downloadFile({
					url: res.data.pdf,
					success: function (res) {
						var filePath = res.tempFilePath;
						uni.openDocument({
							filePath: filePath,
							showMenu: true,
							success: function (res) {
								hideLoading()
								navigateBack()
								console.log('打开文档成功');
							}
						});
					}
				});
			}).catch(err => {
				showToast(err.msg)
			})

		}).catch(err => {
			showToast(err.msg)
		})
	}
	watch(() => state.questionIndex, (questionIndex) => {
		if (questionIndex === 121) {
			state.isClick = false
			state.questionIndex = 120
			clearInterval(timeAddRef.value)
			clearInterval(timeRef.value)
			timeAddRef.value = null
			timeRef.value = null
			addFreeEvaInfo({
				evaConsumTime: state.time,
				outpatientId: userStore.outpatientId,
				signalData: state.answer,
				userName: userStore.userName,
				projectId: props.projectId,
				workorderId: props.workOrderId
			}).then(res => {
				console.log("res: " + JSON.stringify(res));
				qryFreeEvaResultPDF({
					outpatientId: userStore.outpatientId,
					userName: userStore.userName,
					round: res.data.round,
				}).then(res => {
					showLoading('生成报告中....')
					console.log("res: " + JSON.stringify(res));
					console.log(res.data.pdf);
					uni.downloadFile({
						url: res.data.pdf,
						success: function (res) {
							var filePath = res.tempFilePath;
							uni.openDocument({
								filePath: filePath,
								showMenu: true,
								success: function (res) {
									hideLoading()
									console.log('打开文档成功');
								}
							});
						}
					});
				}).catch(err => {
					showToast(err.msg)
				})

			}).catch(err => {
				showToast(err.msg)
			})
		}
	})
	const sliderChange = (e) => {
		innerAudioContextRef.value.volume = e.detail.value
		console.log('value 发生变化：' + e.detail.value)
	}
	const reset = () => {
		if (timeAddRef.value) {
			clearInterval(timeAddRef.value)
			timeAddRef.value = null
		}
		state.questionIndex++
		innerAudioContextRef.value.destroy() //先销毁音频
		innerAudioContextRef.value = null // 再赋值为null
		innerAudioContextRef.value = uni.createInnerAudioContext() // 最后再创建一个新的
		innerAudioContextRef.value.src = `../../static/audio/MP3-${state.questionIndex}.mp3`
		state.audioTime = setTimeout(() => {
			innerAudioContextRef.value.play()
		}, 500)
		state.countdown = 30
		state.clickTime = new Date()
		state.clicked = -1
		state.isClick = true
		timeAdd()
	}
	const pre = () => {
		if (!state.isClick) {
			return
		}
		clearInterval(timeAddRef.value)
		timeAddRef.value = null
		state.questionIndex--
		innerAudioContextRef.value.src = `../../static/audio/MP3-${state.questionIndex}.mp3`
		if (state.audioTime) {
			clearTimeout(state.audioTime)
			state.audioTime = null
		}
		state.audioTime = setTimeout(() => {
			innerAudioContextRef.value.play()
		}, 500)
		state.countdown = 30
		state.clickTime = new Date()
		console.log(Number(state.answer[state.answer.length - 1].split(',')[1]), '------Number(state.answer[state.answer.length - 1].split(', ')[1])');
		state.clicked = state.answer[state.answer.length - 1].split(',')[1] - 1
		state.answer.pop()
		timeAdd()
	}
	const clicked = (index) => {
		if (!state.isClick) {
			return
		}
		if (state.audioTime) {
			clearTimeout(state.audioTime)
			state.audioTime = null
		}
		state.isClick = false
		clearInterval(timeAddRef.value)
		timeAddRef.value = null
		state.answer.push(`${state.trueAnswer[state.questionIndex-1]},${index+1},${new Date()-state.clickTime}`)
		if (state.trueAnswer[state.questionIndex - 1] != index + 1) {
			state.falseSix++
			state.falseAnswer.push('false')
		} else {
			state.falseSix = 0
			state.falseAnswer.push('true')
		}
		state.clicked = index
		state.resetTime = setTimeout(() => {
			reset()
		}, 500)

	}
</script>

<style lang="scss">
	.question {
		width: 100vw;
		height: 100vh;
		background: linear-gradient(206deg, #D4ECF8 0%, #F2F7FC 100%);

		&-top {
			margin-top: 30rpx;

			&-title {
				font-size: 86rpx;
				font-family: PingFangSC-Medium, PingFang SC;
				font-weight: 500;
				color: #111111;
				margin-bottom: 26rpx;
			}
		}

		&-tips {
			padding: 0 102rpx;
			display: flex;
			align-items: center;
			justify-content: space-between;

			&-audio {
				display: flex;
				align-items: center;

				&-box {
					border-radius: 17rpx;
					border: 4rpx solid #111111;
					font-size: 53rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					color: #111111;
					margin-right: 39rpx;
					padding: 13rpx 26rpx;

					&-icon {
						margin-right: 17rpx;
					}
				}

				&-text {
					font-size: 53rpx;
					font-family: PingFangSC-Medium, PingFang SC;
					font-weight: 500;
					color: #111111;
				}
			}

			&-left {
				display: flex;
				align-items: center;
				font-size: 53rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #111111;

				&-icon {
					margin-right: 17rpx;
					font-size: 53rpx;
				}
			}

			&-right {
				font-size: 53rpx;
				font-family: PingFangSC-Regular, PingFang SC;
				font-weight: 400;
				color: #111111;
				display: flex;
				align-items: center;

				&-slider {
					width: 358rpx;
				}
			}
		}

		&-content {
			width: 97%;
			margin: 0 auto;
			background: #FFFFFF;
			box-shadow: 0rpx 17rpx 34rpx 0rpx rgba(212, 226, 245, 1);
			border-radius: 51rpx;
			border: 4rpx solid #FFFFFF;
			margin-top: 50rpx;

			&-img {
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 68rpx 58rpx 52rpx 58rpx;

				&-item {
					display: flex;
					flex-direction: column;
					align-items: center;

					&-box {
						width: 500rpx;
						height: 500rpx;
						border-radius: 32rpx;
						border: 4rpx solid #999999;

						&-value {
							width: 90%;
						}

						&-clicked {
							border: 7rpx solid #41A8DE;
						}
					}

					&-text {
						font-size: 65rpx;
						font-family: PingFangSC-Regular, PingFang SC;
						font-weight: 400;
						color: #111111;
						margin-top: 30rpx;
					}
				}


			}

			&-btn {
				display: flex;
				align-items: center;
				justify-content: space-around;
				padding-bottom: 60rpx;
				width: 90%;
				margin: 0 auto;
				margin-top: 24rpx;

				&-previous {
					border-radius: 94rpx;
					border: 4rpx solid #999999;
					font-size: 56rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					padding: 40rpx 184rpx;

					&-click {
						border-color: #41A8DE;
						color: #41A8DE;
					}
				}

				&-next {
					border-radius: 94rpx;
					border: 4rpx solid #999999;
					font-size: 56rpx;
					font-family: PingFangSC-Regular, PingFang SC;
					font-weight: 400;
					padding: 40rpx 184rpx;

					&-click {
						background: #41A8DE;
						color: #FFFFFF;
					}
				}
			}
		}
	}
</style>