<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <!-- 定义蓝色渐变 -->
  <defs>
    <linearGradient id="blueGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 方形渐变背景 -->
  <rect x="10" y="10" width="180" height="180" rx="20" ry="20" fill="url(#blueGradient)"/>
  
  <!-- 第一行：心理 -->
  <text x="100" y="90" text-anchor="middle" fill="#ffffff" font-family="Arial, Microsoft YaHei, sans-serif" font-size="50" font-weight="bold">心理</text>
  
  <!-- 第二行：测评 -->
  <text x="100" y="150" text-anchor="middle" fill="#ffffff" font-family="Aria<PERSON>, Microsoft YaHei, sans-serif" font-size="50" font-weight="bold">测评</text>
</svg>