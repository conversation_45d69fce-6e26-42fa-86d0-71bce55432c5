/*
 * @Description:ivacpt接口
 * @Author: 小雨
 * @Date: 2023-03-08 09:18:05
 * @LastEditTime: 2023-03-08 13:28:19
 * @LastEditors: 小雨
 */
import httpRequest from '@/utils/interceptors.js';

/**
 * @description: 获取Ivacpt测试数据
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const getIvacptData = (data) => {
	return httpRequest.post('ivacpt/2.0/getIvacptData', data);
};



/**
 * @description: 提交std版IVA_CPT测评数据
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const subIvacptStdData = (data) => {
	return httpRequest.post('ivacpt/v1.1/subIvacptStdData', data);
};

/**
 * @description: 提交mini版IVA_CPT测评数据
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const subIvacptMiniData = (data) => {
	return httpRequest.post('ivacpt/v1.1/subIvacptMiniData', data);
};

/**
 * @description: 导出（标准版）IVA_CPT测评pdf（exportIvacptStdPdf）（新增）
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const exportIvacptStdPdf = (data) => {
	return httpRequest.post('ivacpt/1.0/exportIvacptStdPdf', data);
};

/**
 * @description: 导出（MINI）IVA_CPT测评pdf（exportIvacptMiniPdf）（新增）
 * @author: 小雨
 * @return {*}
 * @param {*} 
 */
export const exportIvacptMiniPdf = (data) => {
	return httpRequest.post('ivacpt/1.0/exportIvacptMiniPdf', data);
};