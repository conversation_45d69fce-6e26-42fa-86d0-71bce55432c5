import httpRequest from '@/utils/interceptors.js';

/**
 * @description: 查询评估套组分类列表
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const qryAssessmentSetTypeList = (data = {}) => {
	return httpRequest.post('assessmentset/v1.1/qryAssessmentSetTypeList', data);
};

/**
 * @description: 查询评估套组项目列表
 * @author: 小雨
 * @return {*}
 * @param {*} data
 */
export const qryAssessmentSetProjectList = (data = {}) => {
	return httpRequest.post('assessmentset/v1.1/qryAssessmentSetProjectList', data);
};

/**
 * @description: 查询评估套组列表
 * @author: 许江涛
 * @return {*}
 * @param {*} data
 */
export const qryAssessmentSetList = (data = {}) => {
    return httpRequest.post('assessmentset/v1.1/qryAssessmentSetList', data);
};

/**
 * @description: 查询评估套组详情
 * @author: 许江涛
 * @return {*}
 * @param {{ assessmentSetId: string }} data
 */
export const qryAssessmentSetDetail = (data = {}) => {
    return httpRequest.post('assessmentset/v1.1/qryAssessmentSetDetail', data);
};

/**
 * @description: 更新评估套组
 * @author: 许江涛
 * @return {*}
 * @param {{ assessmentSetId: string, assessmentSetName: string, projectIds: string[] }} data
 */
export const updateAssessmentSet = (data = {}) => {
    return httpRequest.post('assessmentset/v1.1/updateAssessmentSet', data);
};

/**
 * @description: 新增评估套组
 * @author: 许江涛
 * @return {*}
 * @param {{ assessmentSetName: string, projectIds: string[] }} data
 */
export const addAssessmentSet = (data = {}) => {
    return httpRequest.post('assessmentset/v1.1/addAssessmentSet', data);
};

/**
 * @description: 删除评估套组
 * @author: 许江涛
 * @return {*}
 * @param {{ assessmentSetId: string }} data
 */
export const deleteAssessmentSet = (data = {}) => {
    return httpRequest.post('assessmentset/v1.1/deleteAssessmentSet', data);
};

/**
 * @description: 导出项目评估报告PDF
 * @author: 许江涛
 * @return {*}
 * @param {{ projectId: string, evaluatId: string, traineeId: string }} data
 */
export const exportProjectEvaluationPdf = (data = {}) => {
    return httpRequest.post('project/v1.1/exportProjectEvaluationPdf', data);
};
